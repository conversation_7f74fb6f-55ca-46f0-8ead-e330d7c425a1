const BSPIntegration = require('../lib/bsp-integration');
const logger = require('../lib/logger');
const security = require('../lib/security');

/**
 * API endpoint to initiate payment with BSP gateway
 */
module.exports = async (req, res) => {
    // Apply security headers
    security.securityHeaders(req, res, () => {});

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }

    // Apply rate limiting
    try {
        await new Promise((resolve, reject) => {
            security.rateLimit(req, res, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    } catch (error) {
        return; // Rate limit response already sent
    }

    // Check for suspicious IP
    const clientIP = security.getClientIP(req);
    if (security.isSuspiciousIP(clientIP)) {
        logger.logSecurity('Suspicious IP blocked', { ip: clientIP });
        res.status(403).json({ error: 'Access denied' });
        return;
    }
    
    try {
        const donationData = req.body;
        
        // Log payment initiation
        logger.info('Payment initiation started', {
            amount: donationData.amount,
            email: donationData.email,
            ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
        });
        
        // Validate input data using security module
        const validation = security.validatePaymentData(donationData);
        if (!validation.isValid) {
            logger.warn('Payment validation failed', {
                errors: validation.errors,
                ip: clientIP
            });
            res.status(400).json({
                error: 'Validation failed',
                details: validation.errors
            });
            return;
        }

        // Use sanitized data
        const sanitizedData = validation.data;
        
        // Initialize BSP integration
        const bsp = new BSPIntegration();
        
        // Prepare payment request with sanitized data
        const paymentData = bsp.preparePaymentRequest(sanitizedData);
        
        // Log transaction
        logger.logTransaction('payment_initiated', {
            orderId: paymentData.orderId,
            amount: sanitizedData.amount,
            customerEmail: sanitizedData.email,
            ip: clientIP
        });
        
        // Create payment form HTML for auto-submission
        const paymentForm = bsp.createPaymentForm(paymentData);
        
        // Return success response with redirect
        res.status(200).json({
            success: true,
            orderId: paymentData.orderId,
            redirectUrl: `/api/payment-redirect?orderId=${paymentData.orderId}`,
            message: 'Payment request prepared successfully'
        });
        
        // Store payment form temporarily (in production, use Redis or database)
        global.paymentForms = global.paymentForms || {};
        global.paymentForms[paymentData.orderId] = {
            html: paymentForm,
            data: paymentData,
            created: Date.now()
        };
        
        // Clean up old forms (older than 10 minutes)
        cleanupOldForms();
        
    } catch (error) {
        logger.error('Payment initiation error', { 
            error: error.message, 
            stack: error.stack 
        });
        
        res.status(500).json({ 
            error: 'Payment initiation failed', 
            message: 'An error occurred while preparing your payment. Please try again.' 
        });
    }
};



/**
 * Clean up old payment forms from memory
 */
function cleanupOldForms() {
    if (!global.paymentForms) return;
    
    const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
    
    Object.keys(global.paymentForms).forEach(orderId => {
        if (global.paymentForms[orderId].created < tenMinutesAgo) {
            delete global.paymentForms[orderId];
        }
    });
}

{"name": "bsp-ipg-test-integration", "version": "1.0.0", "description": "BSP Internet Payment Gateway test integration for SPCA Fiji donations", "main": "api/index.js", "scripts": {"dev": "vercel dev", "build": "echo 'Build complete'", "start": "vercel dev", "deploy": "vercel --prod", "deploy-preview": "vercel", "logs": "vercel logs", "env": "vercel env ls", "test": "echo 'No tests specified'"}, "keywords": ["bsp", "payment-gateway", "spca-fiji", "donations", "vercel"], "author": "SPCA Fiji", "license": "MIT", "dependencies": {"crypto": "^1.0.1", "express": "^4.18.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "uuid": "^9.0.0", "validator": "^13.11.0"}, "devDependencies": {"@vercel/node": "^3.0.0"}, "engines": {"node": ">=18.0.0"}}
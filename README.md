# SPCA Fiji Donation Portal - BSP IPG Integration

A secure donation portal for SPCA Fiji integrated with BSP Bank's Internet Payment Gateway (IPG) for processing online donations.

## Features

-   **Secure Payment Processing**: Integration with BSP Bank's IPG for secure card payments
-   **User-Friendly Interface**: Clean, responsive donation form with multiple amount options
-   **Security**: HMAC signature verification, rate limiting, input validation, and security headers
-   **Comprehensive Logging**: Transaction logging, error tracking, and security monitoring
-   **Mobile Responsive**: Optimized for all device sizes
-   **Production Ready**: Configured for Vercel deployment with environment variables

## Technology Stack

-   **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
-   **Backend**: Node.js with Vercel serverless functions
-   **Payment Gateway**: BSP Bank Internet Payment Gateway
-   **Security**: HMAC-SHA256 signatures, rate limiting, input sanitization
-   **Deployment**: Vercel platform

## Project Structure

```
├── api/                          # Vercel serverless functions
│   ├── initiate-payment.js       # Payment initiation endpoint
│   ├── payment-callback.js       # BSP callback handler
│   ├── payment-redirect.js       # Payment form redirect
│   └── payment-return.js         # BSP return URL handler
├── lib/                          # Utility libraries
│   ├── bsp-integration.js        # BSP IPG integration logic
│   ├── logger.js                 # Logging utilities
│   └── security.js               # Security and validation
├── public/                       # Static files
│   ├── index.html                # Main donation form
│   ├── thank-you.html            # Success page
│   ├── cancel.html               # Cancellation page
│   ├── styles.css                # Styling
│   └── script.js                 # Frontend JavaScript
├── .env.local                  # Environment variables template
├── .gitignore                    # Git ignore rules
├── package.json                  # Dependencies and scripts
├── vercel.json                   # Vercel configuration
└── README.md                     # This file
```

## Setup Instructions

### 1. Clone and Install

```bash
git clone <repository-url>
cd donations.spcafiji.com
npm install
```

### 2. Environment Configuration

Copy the environment template and fill in your BSP credentials:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your actual BSP credentials:

```env
# BSP IPG Production Credentials
BSP_HMAC_KEY=your_actual_hmac_key_here
MERCHANT_ID=800126108001016
MERCHANT_SID=8001016
MERCHANT_TID=80011601
MERCHANT_NAME=SPCA Fiji
MCC_CODE=8398

# URLs
BSP_PAYMENT_URL=https://oceania.thecardservicesonline.com/bsppg/mercpg
RETURN_URL=https://donations.spcafiji.com/api/payment-return
WEBSITE_ADDRESS=https://donations.spcafiji.com

# Application
NODE_ENV=production
LOG_LEVEL=info
```

### 3. Local Development

```bash
# Install Vercel CLI
npm install -g vercel

# Start local development server
vercel dev
```

The application will be available at `http://localhost:3000`

### 4. Deployment to Vercel

```bash
# Login to Vercel
vercel login

# Deploy to production
vercel --prod
```

## Environment Variables

Configure these environment variables in your Vercel dashboard:

| Variable          | Description                | Required |
| ----------------- | -------------------------- | -------- |
| `BSP_HMAC_KEY`    | HMAC key provided by BSP   | Yes      |
| `MERCHANT_ID`     | BSP Merchant ID            | Yes      |
| `MERCHANT_SID`    | BSP Merchant SID           | Yes      |
| `MERCHANT_TID`    | BSP Merchant TID           | Yes      |
| `MERCHANT_NAME`   | Merchant display name      | Yes      |
| `MCC_CODE`        | Merchant Category Code     | Yes      |
| `BSP_PAYMENT_URL` | BSP payment gateway URL    | Yes      |
| `RETURN_URL`      | Return URL after payment   | Yes      |
| `WEBSITE_ADDRESS` | Your website domain        | Yes      |
| `NODE_ENV`        | Environment (production)   | Yes      |
| `LOG_LEVEL`       | Logging level (info/debug) | No       |

## API Endpoints

### POST /api/initiate-payment

Initiates a payment with BSP gateway.

**Request Body:**

```json
{
	"amount": 50,
	"firstName": "John",
	"lastName": "Doe",
	"email": "<EMAIL>",
	"phone": "+6791234567",
	"purpose": "general",
	"terms": true
}
```

### POST /api/payment-callback

Handles BSP payment notifications (webhook).

### GET /api/payment-return

Handles user return from BSP gateway.

### GET /api/payment-redirect

Serves auto-submitting payment form.

## Security Features

-   **HMAC Signature Verification**: All BSP communications are verified
-   **Rate Limiting**: Prevents abuse with configurable limits
-   **Input Validation**: Comprehensive validation and sanitization
-   **Security Headers**: OWASP recommended security headers
-   **IP Monitoring**: Suspicious IP detection and blocking
-   **Secure Logging**: Sensitive data is automatically sanitized

## Testing

### Test Payment Flow

1. Visit the donation form
2. Fill in donation details
3. Submit the form
4. You'll be redirected to BSP's test environment
5. Use BSP's test card numbers for testing
6. Complete the payment flow

### Test Card Numbers (BSP Test Environment)

-   **Successful Payment**: ****************
-   **Declined Payment**: ****************
-   **Insufficient Funds**: ****************

## Monitoring and Logging

The application includes comprehensive logging:

-   **Transaction Logs**: All payment attempts and outcomes
-   **Security Logs**: Failed authentications, rate limit violations
-   **Error Logs**: Application errors and exceptions
-   **Debug Logs**: Detailed debugging information

Logs are output in JSON format for easy parsing and monitoring.

## Support

For technical support or questions about the BSP integration:

-   **BSP Support**: Contact your BSP account manager
-   **Application Issues**: Check the logs in Vercel dashboard
-   **SPCA Fiji**: <EMAIL>

## License

This project is proprietary to SPCA Fiji. All rights reserved.

## Security Notice

-   Never commit `.env.local` or any files containing credentials
-   Regularly rotate HMAC keys and other sensitive credentials
-   Monitor logs for suspicious activity
-   Keep dependencies updated for security patches

## Changelog

### v1.0.0 (2024-01-XX)

-   Initial release with BSP IPG integration
-   Complete payment flow implementation
-   Security measures and validation
-   Production-ready deployment configuration

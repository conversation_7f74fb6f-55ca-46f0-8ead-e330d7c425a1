# BSP IPG Production Credentials - SPCA Fiji
# Copy this file to .env.local and fill in the actual values
# Keep this file secure and never commit to version control

# HMAC Key for production environment (provided by <PERSON><PERSON>)
BSP_HMAC_KEY=

# BSP Merchant Portal Credentials
BSP_MERCHANT_ID=
BSP_MERCHANT_PASSWORD=

# Merchant Configuration
MERCHANT_ID=
MERCHANT_SID=
MERCHANT_TID=
MCC_CODE=
MERCHANT_NAME=Society for the Prevention of Cruelty to Animals (SPCA) Fiji Islands

# URLs
BSP_PAYMENT_URL=
BSP_PORTAL_URL=
RETURN_URL=
WEBSITE_ADDRESS=

# Application Configuration
NODE_ENV= #development/production
PORT=3000
LOG_LEVEL=info

# Security
SESSION_SECRET=
ENCRYPTION_KEY=

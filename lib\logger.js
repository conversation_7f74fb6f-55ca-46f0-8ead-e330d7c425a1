/**
 * Simple logging utility for BSP payment integration
 */
class Logger {
    constructor() {
        this.logLevel = process.env.LOG_LEVEL || 'info';
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
    }
    
    /**
     * Check if log level should be output
     * @param {string} level - Log level
     * @returns {boolean} Should log
     */
    shouldLog(level) {
        return this.levels[level] <= this.levels[this.logLevel];
    }
    
    /**
     * Format log message
     * @param {string} level - Log level
     * @param {string} message - Log message
     * @param {Object} data - Additional data
     * @returns {Object} Formatted log entry
     */
    formatMessage(level, message, data = {}) {
        return {
            timestamp: new Date().toISOString(),
            level: level.toUpperCase(),
            message,
            data: this.sanitizeData(data),
            environment: process.env.NODE_ENV || 'development'
        };
    }
    
    /**
     * Sanitize sensitive data from logs
     * @param {Object} data - Data to sanitize
     * @returns {Object} Sanitized data
     */
    sanitizeData(data) {
        if (!data || typeof data !== 'object') return data;
        
        const sensitiveFields = [
            'password', 'hmacKey', 'signature', 'cardNumber', 
            'cvv', 'pin', 'token', 'secret', 'key'
        ];
        
        const sanitized = { ...data };
        
        const sanitizeObject = (obj) => {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    const lowerKey = key.toLowerCase();
                    
                    // Check if field contains sensitive data
                    if (sensitiveFields.some(field => lowerKey.includes(field))) {
                        if (typeof obj[key] === 'string' && obj[key].length > 4) {
                            obj[key] = obj[key].substring(0, 4) + '***';
                        } else {
                            obj[key] = '***';
                        }
                    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                        sanitizeObject(obj[key]);
                    }
                }
            }
        };
        
        sanitizeObject(sanitized);
        return sanitized;
    }
    
    /**
     * Log error message
     * @param {string} message - Error message
     * @param {Object} data - Additional data
     */
    error(message, data = {}) {
        if (this.shouldLog('error')) {
            const logEntry = this.formatMessage('error', message, data);
            console.error(JSON.stringify(logEntry, null, 2));
        }
    }
    
    /**
     * Log warning message
     * @param {string} message - Warning message
     * @param {Object} data - Additional data
     */
    warn(message, data = {}) {
        if (this.shouldLog('warn')) {
            const logEntry = this.formatMessage('warn', message, data);
            console.warn(JSON.stringify(logEntry, null, 2));
        }
    }
    
    /**
     * Log info message
     * @param {string} message - Info message
     * @param {Object} data - Additional data
     */
    info(message, data = {}) {
        if (this.shouldLog('info')) {
            const logEntry = this.formatMessage('info', message, data);
            console.log(JSON.stringify(logEntry, null, 2));
        }
    }
    
    /**
     * Log debug message
     * @param {string} message - Debug message
     * @param {Object} data - Additional data
     */
    debug(message, data = {}) {
        if (this.shouldLog('debug')) {
            const logEntry = this.formatMessage('debug', message, data);
            console.log(JSON.stringify(logEntry, null, 2));
        }
    }
    
    /**
     * Log payment transaction
     * @param {string} action - Payment action
     * @param {Object} data - Transaction data
     */
    logTransaction(action, data = {}) {
        this.info(`Payment Transaction: ${action}`, {
            action,
            orderId: data.orderId,
            amount: data.amount,
            status: data.status,
            transactionId: data.transactionId,
            customerEmail: data.customerEmail,
            timestamp: data.timestamp || new Date().toISOString()
        });
    }
    
    /**
     * Log security event
     * @param {string} event - Security event
     * @param {Object} data - Event data
     */
    logSecurity(event, data = {}) {
        this.warn(`Security Event: ${event}`, {
            event,
            ip: data.ip,
            userAgent: data.userAgent,
            timestamp: new Date().toISOString(),
            details: data.details
        });
    }
}

// Create singleton instance
const logger = new Logger();

module.exports = logger;

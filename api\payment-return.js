const BSPIntegration = require('../lib/bsp-integration');
const logger = require('../lib/logger');

/**
 * API endpoint to handle BSP payment gateway return URL
 * This is where users are redirected after completing payment on BSP gateway
 */
module.exports = async (req, res) => {
    try {
        // Log return request
        logger.info('Payment return received', {
            method: req.method,
            query: req.query,
            ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
        });
        
        // BSP can send return data via GET or POST
        const returnData = req.method === 'POST' ? req.body : req.query;
        
        // Initialize BSP integration
        const bsp = new BSPIntegration();
        
        // Verify return data signature if present
        let isValidSignature = true;
        if (returnData.signature) {
            isValidSignature = bsp.verifyCallback(returnData);
            
            if (!isValidSignature) {
                logger.logSecurity('Invalid return signature', {
                    ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
                    userAgent: req.headers['user-agent'],
                    details: 'BSP return signature verification failed'
                });
            }
        }
        
        // Parse payment status
        const paymentStatus = bsp.parsePaymentStatus(returnData);
        
        // Log the return
        logger.logTransaction('payment_return', paymentStatus);
        
        // Determine redirect URL based on payment status
        let redirectUrl;
        const baseUrl = process.env.WEBSITE_ADDRESS || req.headers.host;
        
        switch (paymentStatus.status) {
            case 'success':
                // Redirect to thank you page with transaction details
                redirectUrl = `/thank-you.html?orderId=${encodeURIComponent(paymentStatus.orderId)}&transactionId=${encodeURIComponent(paymentStatus.transactionId || '')}&amount=${encodeURIComponent(paymentStatus.amount || '')}&authCode=${encodeURIComponent(paymentStatus.authCode || '')}`;
                break;

            case 'failed':
                // Redirect to error page with failure reason and specific error message
                const errorMessage = paymentStatus.message || 'Payment declined';
                redirectUrl = `/cancel.html?orderId=${encodeURIComponent(paymentStatus.orderId)}&reason=declined&message=${encodeURIComponent(errorMessage)}&authCode=${encodeURIComponent(paymentStatus.authCode || '')}`;
                break;

            case 'cancelled':
                // User cancelled the transaction
                redirectUrl = `/cancel.html?orderId=${encodeURIComponent(paymentStatus.orderId)}&reason=cancelled&message=${encodeURIComponent(paymentStatus.message || 'Transaction cancelled by user')}`;
                break;

            case 'pending':
                // Redirect to pending page or thank you with pending status
                redirectUrl = `/thank-you.html?orderId=${encodeURIComponent(paymentStatus.orderId)}&status=pending&message=${encodeURIComponent(paymentStatus.message || 'Payment is being processed')}`;
                break;

            default:
                // Unknown status - redirect to cancel page
                redirectUrl = `/cancel.html?orderId=${encodeURIComponent(paymentStatus.orderId || 'unknown')}&reason=error&message=${encodeURIComponent(paymentStatus.message || 'Unknown payment status')}`;
                break;
        }
        
        // Store payment result for potential later retrieval
        global.paymentResults = global.paymentResults || {};
        global.paymentResults[paymentStatus.orderId] = {
            status: paymentStatus.status,
            data: paymentStatus,
            timestamp: new Date().toISOString(),
            signatureValid: isValidSignature
        };
        
        // Redirect user to appropriate page
        res.redirect(302, redirectUrl);
        
    } catch (error) {
        logger.error('Payment return processing error', { 
            error: error.message, 
            stack: error.stack,
            query: req.query,
            body: req.body
        });
        
        // Redirect to error page on any processing error
        res.redirect(302, '/cancel.html?reason=error');
    }
};

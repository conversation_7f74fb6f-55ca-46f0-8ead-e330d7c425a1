/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
}

.logo h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.logo p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    font-weight: 300;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.payment-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
}

.payment-card h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    text-align: center;
}

.subtitle {
    text-align: center;
    color: #718096;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
}

/* Amount Selection */
.amount-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.amount-btn {
    padding: 12px 20px;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.amount-btn:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.amount-btn.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

/* Form Elements */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #4a5568;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.custom-amount input {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Payment Methods */
.payment-methods {
    margin-bottom: 10px;
}

.payment-method {
    display: block;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 10px;
}

.payment-method:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.payment-method input[type="radio"] {
    display: none;
}

.payment-method input[type="radio"]:checked + .payment-method-label {
    color: #667eea;
}

.payment-method input[type="radio"]:checked {
    border-color: #667eea;
    background: #f7fafc;
}

.payment-method-label {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.payment-icon {
    margin-right: 10px;
    font-size: 1.2rem;
}

.payment-note {
    font-size: 0.9rem;
    color: #718096;
    text-align: center;
}

/* Checkboxes */
.checkbox-group {
    margin-bottom: 15px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    margin-right: 10px;
    margin-top: 2px;
    flex-shrink: 0;
    transition: all 0.2s ease;
    position: relative;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Submit Button */
.donate-btn {
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.donate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.donate-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loader {
    display: inline-block;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 30px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Links */
a {
    color: #667eea;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .payment-card {
        padding: 25px;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .amount-options {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .payment-card {
        padding: 20px;
    }
    
    .amount-options {
        grid-template-columns: 1fr;
    }
}

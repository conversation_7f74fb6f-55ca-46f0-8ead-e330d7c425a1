<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thank You - SPCA Fiji</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .success-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }
        
        .success-card {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 40px;
            color: white;
        }
        
        .success-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .success-subtitle {
            font-size: 1.2rem;
            color: #718096;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .donation-details {
            background: #f7fafc;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .detail-row:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 1.1rem;
            color: #2d3748;
        }
        
        .detail-label {
            color: #4a5568;
            font-weight: 500;
        }
        
        .detail-value {
            color: #2d3748;
            font-weight: 600;
        }
        
        .next-steps {
            background: #ebf8ff;
            border: 1px solid #bee3f8;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .next-steps h3 {
            color: #2b6cb0;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .next-steps ul {
            list-style: none;
            padding: 0;
        }
        
        .next-steps li {
            padding: 8px 0;
            color: #2d3748;
            position: relative;
            padding-left: 25px;
        }
        
        .next-steps li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #38a169;
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }
        
        .social-share {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
        }
        
        .social-share h3 {
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        .social-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .social-btn {
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .social-btn.facebook {
            background: #1877f2;
            color: white;
        }
        
        .social-btn.twitter {
            background: #1da1f2;
            color: white;
        }
        
        .social-btn.email {
            background: #718096;
            color: white;
        }
        
        .social-btn:hover {
            transform: translateY(-2px);
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .success-card {
                padding: 25px;
            }
            
            .success-title {
                font-size: 2rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
            
            .social-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .social-btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <h1>SPCA Fiji</h1>
                <p>Society for the Prevention of Cruelty to Animals</p>
            </div>
        </header>

        <main class="success-container">
            <div class="success-card">
                <div class="success-icon">
                    ✓
                </div>
                
                <h1 class="success-title">Thank You!</h1>
                <p class="success-subtitle">
                    Your generous donation has been successfully processed. 
                    Your support means the world to the animals in our care.
                </p>
                
                <div class="donation-details" id="donationDetails">
                    <div class="detail-row">
                        <span class="detail-label">Transaction ID:</span>
                        <span class="detail-value" id="transactionId">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value" id="donationDate">Loading...</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Purpose:</span>
                        <span class="detail-value" id="donationPurpose">General Fund</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Amount:</span>
                        <span class="detail-value" id="donationAmount">Loading...</span>
                    </div>
                </div>
                
                <div class="next-steps">
                    <h3>What happens next?</h3>
                    <ul>
                        <li>You will receive an email confirmation within the next few minutes</li>
                        <li>Your donation will be used to provide care, shelter, and medical treatment for animals in need</li>
                        <li>You can track our impact and see how your donation helps through our regular updates</li>
                        <li>A tax-deductible receipt will be sent to your email address</li>
                    </ul>
                </div>
                
                <div class="action-buttons">
                    <a href="/" class="btn btn-primary">Make Another Donation</a>
                    <a href="https://spcafiji.com" class="btn btn-secondary" target="_blank">Visit Our Website</a>
                </div>
                
                <div class="social-share">
                    <h3>Help us spread the word</h3>
                    <div class="social-buttons">
                        <a href="#" class="social-btn facebook" onclick="shareOnFacebook()">
                            📘 Share on Facebook
                        </a>
                        <a href="#" class="social-btn twitter" onclick="shareOnTwitter()">
                            🐦 Share on Twitter
                        </a>
                        <a href="#" class="social-btn email" onclick="shareViaEmail()">
                            ✉️ Share via Email
                        </a>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 SPCA Fiji. All rights reserved. | Thank you for your support!</p>
        </footer>
    </div>

    <script>
        // Load donation details from URL parameters or localStorage
        document.addEventListener('DOMContentLoaded', function() {
            loadDonationDetails();
        });
        
        function loadDonationDetails() {
            const urlParams = new URLSearchParams(window.location.search);
            const orderId = urlParams.get('orderId');
            const amount = urlParams.get('amount');
            const transactionId = urlParams.get('transactionId');
            
            // Set current date
            document.getElementById('donationDate').textContent = new Date().toLocaleDateString();
            
            // Set transaction details if available
            if (transactionId) {
                document.getElementById('transactionId').textContent = transactionId;
            } else if (orderId) {
                document.getElementById('transactionId').textContent = orderId;
            } else {
                document.getElementById('transactionId').textContent = 'Confirmation pending';
            }
            
            // Set amount if available
            if (amount) {
                document.getElementById('donationAmount').textContent = formatCurrency(parseFloat(amount));
            } else {
                document.getElementById('donationAmount').textContent = 'See email confirmation';
            }
            
            // Set purpose from localStorage if available
            const purpose = localStorage.getItem('donationPurpose');
            if (purpose) {
                document.getElementById('donationPurpose').textContent = purpose;
                localStorage.removeItem('donationPurpose'); // Clean up
            }
        }
        
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-FJ', {
                style: 'currency',
                currency: 'FJD',
                minimumFractionDigits: 2
            }).format(amount);
        }
        
        function shareOnFacebook() {
            const url = encodeURIComponent(window.location.origin);
            const text = encodeURIComponent('I just donated to SPCA Fiji to help animals in need. Join me in supporting this great cause!');
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
        }
        
        function shareOnTwitter() {
            const url = encodeURIComponent(window.location.origin);
            const text = encodeURIComponent('I just donated to @SPCAFiji to help animals in need. Join me in supporting this great cause!');
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
        }
        
        function shareViaEmail() {
            const subject = encodeURIComponent('Support SPCA Fiji - Help Animals in Need');
            const body = encodeURIComponent(`Hi,\n\nI just made a donation to SPCA Fiji to help animals in need, and I thought you might be interested in supporting this great cause too.\n\nYou can make a donation at: ${window.location.origin}\n\nEvery donation, no matter the size, makes a real difference in the lives of animals across Fiji.\n\nThanks for considering it!`);
            window.location.href = `mailto:?subject=${subject}&body=${body}`;
        }
    </script>
</body>
</html>

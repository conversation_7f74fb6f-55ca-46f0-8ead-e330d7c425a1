<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPCA Fiji - Donation Payment</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <h1>SPCA Fiji</h1>
                <p>Society for the Prevention of Cruelty to Animals</p>
            </div>
        </header>

        <main class="main-content">
            <div class="payment-card">
                <h2>Make a Donation</h2>
                <p class="subtitle">Help us care for animals in need across Fiji</p>

                <form id="donationForm" class="donation-form">
                    <!-- Donation Amount Section -->
                    <div class="form-section">
                        <h3>Donation Amount</h3>
                        <div class="amount-options">
                            <button type="button" class="amount-btn" data-amount="25">$25</button>
                            <button type="button" class="amount-btn" data-amount="50">$50</button>
                            <button type="button" class="amount-btn" data-amount="100">$100</button>
                            <button type="button" class="amount-btn" data-amount="250">$250</button>
                        </div>
                        <div class="custom-amount">
                            <label for="customAmount">Custom Amount (FJD)</label>
                            <input type="number" id="customAmount" name="amount" min="5" step="0.01" placeholder="Enter amount" required>
                        </div>
                    </div>

                    <!-- Donor Information Section -->
                    <div class="form-section">
                        <h3>Donor Information</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">First Name *</label>
                                <input type="text" id="firstName" name="firstName" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name *</label>
                                <input type="text" id="lastName" name="lastName" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="city">City</label>
                                <input type="text" id="city" name="city">
                            </div>
                            <div class="form-group">
                                <label for="country">Country</label>
                                <select id="country" name="country">
                                    <option value="FJ">Fiji</option>
                                    <option value="AU">Australia</option>
                                    <option value="NZ">New Zealand</option>
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method Section -->
                    <div class="form-section">
                        <h3>Payment Method</h3>
                        <div class="payment-methods">
                            <label class="payment-method">
                                <input type="radio" name="paymentMethod" value="card" checked>
                                <span class="payment-method-label">
                                    <span class="payment-icon">💳</span>
                                    Credit/Debit Card
                                </span>
                            </label>
                        </div>
                        <p class="payment-note">Secure payment powered by BSP Bank</p>
                    </div>

                    <!-- Donation Purpose Section -->
                    <div class="form-section">
                        <h3>Donation Purpose (Optional)</h3>
                        <div class="form-group">
                            <label for="purpose">How would you like your donation to be used?</label>
                            <select id="purpose" name="purpose">
                                <option value="general">General Fund - Where needed most</option>
                                <option value="medical">Medical Care & Treatment</option>
                                <option value="shelter">Animal Shelter & Housing</option>
                                <option value="rescue">Animal Rescue Operations</option>
                                <option value="education">Community Education Programs</option>
                                <option value="emergency">Emergency Relief Fund</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="message">Message (Optional)</label>
                            <textarea id="message" name="message" rows="3" placeholder="Leave a message of support..."></textarea>
                        </div>
                    </div>

                    <!-- Terms and Submit -->
                    <div class="form-section">
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="terms" name="terms" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="newsletter" name="newsletter">
                                <span class="checkmark"></span>
                                I would like to receive updates about SPCA Fiji's work
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="donate-btn" id="donateBtn">
                        <span class="btn-text">Proceed to Payment</span>
                        <span class="btn-loader" style="display: none;">Processing...</span>
                    </button>
                </form>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 SPCA Fiji. All rights reserved. | Secure payments by BSP Bank</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>

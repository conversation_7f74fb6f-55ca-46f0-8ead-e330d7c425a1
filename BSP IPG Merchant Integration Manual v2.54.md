# Merchant Integration Manual

## For

## BSP Internet Payment Gateway

---

## 1.1 List of Definitions

Following is the list of definitions used in this document:

| No  | Terms          | Definitions                                                                                         |
| --- | -------------- | --------------------------------------------------------------------------------------------------- |
| 1.  | Authentication | A process to be undertaken by the relevant party that validates the identity and integrity of data. |
| 2.  | Authorization  | A process where the financial institution approves a transaction.                                   |
| 3.  | Remitter       | A customer that makes payment via NARADA® SECURE.                                                   |
| 4.  | Remitter Bank  | A participating financial institution where the remitter maintains a valid Card account.            |
| 5.  | Merchant       | An agency that receives payment from remitter via NARADA® SECURE.                                   |
| 6.  | Merchant Bank  | A financial institution where the Merchant maintains its valid banking account.                     |

## 1.2 List of Abbreviations

Following is the list of abbreviations used in this document:

| No  | Abbreviation | Description                |
| --- | ------------ | -------------------------- |
| 1.  | AR           | Authorization Request      |
| 2.  | AC           | Authorization Confirmation |
| 3.  | AS           | Authorization Status       |

## 1.3 User Flow

Following is the user flow for the standard integration:

**Step 1: Checkout & Redirection** – Remitter selects checkout at Merchant website and will be redirected to NARADA® SECURE page.

**Step 2: Card details capture** – NARADA® SECURE will display features available at NARADA® SECURE entry page:

a. Displays transaction information and purchase description.
b. Option to remitter to select the remitter (BSP Bank) Card for payment.
c. Option to capture the Card number and other details from remitter.
d. Displays links to useful information - NARADA Helpdesk contact number, FAQ, Terms & Conditions.

**Step 3: Authentication** – NARADA® SECURE will send these Card details to the BSP bank Authentication Server where the authentication is done by sending a numeric OTP to remitter (Sent by BSP bank)

**Step 4: Authorization** – NARADA® SECURE will ask the remitter to key-in the OTP received in their mobile. And the same will be sent to BSP bank Authorization Server for authorization.

**Step 5: Authorization to NARADA® SECURE** – After completion of payment, authorization details will be sent back to NARADA® SECURE page. NARADA® SECURE will show the transaction status and will send it to the Merchant website.

**Step 6: URL Redirection to Merchant** – Merchant website will display Merchant receipt to the remitter.

## 1.4 Transaction Flow

**Step 1 – Initiation of a transaction by the remitter**
Remitter makes access to the Merchant website to make payment for a service, and proceeds for payment.

**Step 2 – AR Message to NARADA® SECURE**
Once remitter selects "Pay Online" as his payment option, Merchant website redirects the remitter to NARADA® SECURE entry page. During redirection, Merchant website sends AR message to NARADA® SECURE via SSL.

**Step 3 – Selection of remitter bank**
Remitter keys-in their Card number details at the NARADA® SECURE entry page.

**Step 4 – Authentication NARADA® SECURE**
NARADA® SECURE sends the Card account enquiry request message which carries customer's Card account details to BSP bank.

**Step 5 – Sending OTP to remitter**
Once the BSP bank successfully authenticates the account number, it sends OTP to remitter's registered mobile number.

**Step 6 – Validation of OTP & performing debiting**
NARADA® SECURE asks the remitter to key-in the received OTP and sends debit request with OTP to BSP bank. BSP Bank validates the OTP and performs authorization on remitter bank account.

**Step 7 – Authorization NARADA® SECURE**
BSP bank sends debit response message which carries authorization details to NARADA® SECURE.

**Step 8 – Status display to remitter**
NARADA® SECURE displays transaction status page to the remitter.

**Step 9 – Redirecting remitter to Merchant website**
NARADA® SECURE redirects the remitter to Merchant website. During this redirection, NARADA® SECURE send AC message to Merchant website.

**Step 10 – Merchant website status display**
Merchant website displays transaction status page to the remitter.

---

## 2. NARADA® SECURE Message

### 2.1 Field Type

Below are the types of fields supported by NARADA® SECURE:

| Field Type | Field Name | Field Description                                   |
| ---------- | ---------- | --------------------------------------------------- |
| N          | Numeric    | Number value in the range of 0-9                    |
| D          | Date time  | Date and time Timestamp (YYYYMMDDHH24MISS) in FJT   |
| C          | Character  | Alphanumeric value in the range of A-Z, a-z and 0-9 |

**Table 1 - Field Type Description**

### 2.2 Field Classification

Below are the field classifications in NARADA® SECURE:

| Field Type | Description |
| ---------- | ----------- |
| M          | Mandatory   |
| O          | Optional    |

**Table 2 - Field Classification**

### 2.3 Special Character Handling

Below is the list of special characters supported by NARADA® SECURE:

| No  | Special Character | Description   |
| --- | ----------------- | ------------- |
| 1.  | @                 | At-sign       |
| 2.  | /                 | Slash         |
| 3.  | \                 | Backslash     |
| 4.  | (                 | Open bracket  |
| 5.  | )                 | Close bracket |
| 6.  |                   | Blank space   |
| 7.  | .                 | Full stop     |
| 8.  | -                 | Hyphen        |
| 9.  | \_                | Underscore    |
| 10. | ,                 | Comma         |
| 11. | &                 | Ampersand     |

**Table 3 - Special Characters Supported by NARADA® SECURE Service**

### 2.4 Request Message

Merchant will send AR and AS messages to NARADA® SECURE. All messages are in Name / Value Pair (NVP) elements inside an HTML FORM. The name of the request fields must have a prefix "nar" as a message identifier.

#### 2.4.1 AR Message

AR message serves as a starting point for the Merchant to request for authorization from the remitter bank. It contains the transaction information from the remitter, which is inclusive of Merchant ID, Merchant order number, and the Transaction amount.

#### 2.4.2 AS Message

AS message serves as a query to obtain the current status of the transaction. This is important in the event that the Merchant does not receive any AC message from NARADA® SECURE.

### 2.5 Request Message Format – AR, AS

#### 2.5.1 Message Format

Below are the request message formats of elements for AR and AS messages:

| No  | Data Element       | Field Type | Data Type      | Length | Notes                                                                                                                                              |
| --- | ------------------ | ---------- | -------------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | nar_msgType        | M          | C              | 2      | Indicates message type. AR / AS                                                                                                                    |
| 2   | nar_merTxnTime     | M          | D              | 14     | Date and time of transaction originated from Merchant. YYYYMMDDHHMMSS                                                                              |
| 3   | nar_orderNo        | M          | C              | 40     | Unique order number generated by Merchant.                                                                                                         |
| 4   | nar_merId          | M          | C              | 15     | Merchant ID - provided by NARADA® Secure. E.g. ***************                                                                                     |
| 5   | nar_merBankCode    | M          | N              | 2      | Merchant account number registered in NARADA® Secure. 01 / 02 / 03 / 04 Default value = 01                                                         |
| 6   | nar_txnCurrency    | M          | C              | 3      | Transaction currency. Default value = 242(FJD)                                                                                                     |
| 7   | nar_txnAmount      | M          | N              | 16,2   | Total amount.                                                                                                                                      |
| 8   | nar_remitterEmail  | O          | EMAIL          | 50     | Remitter's email address.                                                                                                                          |
| 9   | nar_remitterMobile | O          | N              | 10     | Remitter's mobile No                                                                                                                               |
| 10  | nar_cardType       | O          | VI/MC/BD/BC/EX | 2      | VI- Visa Offus MC-Mater Card Offus BD- BSP Onus Cards BD – BSP Debit Cards BC – BSP Credit Cards EX- If card type not able to define               |
| 11  | nar_checkSum       | M          | C              | -      | Checksum value. Hold message signature.                                                                                                            |
| 12  | nar_paymentDesc    | M          | C, SC          | 30     | Payment description.                                                                                                                               |
| 13  | nar_version        | M          | N              | 3      | Version number. Default value = 1.0                                                                                                                |
| 14  | nar_returnUrl      | M          | C              | 200    | Return URL of Merchant to which IPG will send back the response                                                                                    |
| 15  | nar_mcccode        | M          | N              | 4      | Merchant Code provided by BSP. Example : 4112                                                                                                      |
| 16  | nar_Secure         | M          | C              | 9      | This is a default value to know the type of algorithm is opted weather it is HMAC SHA-256(MERSECURE) or open SSL RSA public-private key(IPGSECURE) |

**Table 4 - Request Message Format AR & AS**

#### 2.5.2 Sample Message

Below is the sample of NVP form element:

```html
<form name="myform" action="https://uat2.yalamanchili.in/MPI_v1/mercpg" method="post">
	<input type="hidden" id="nar_msgType" name="nar_msgType" value="AR" />
	<input type="hidden" id="nar_merTxnTime" name="nar_merTxnTime" value="**************" />
	<input type="hidden" id="nar_merBankCode" name="nar_merBankCode" value="01" />
	<input type="hidden" id="nar_orderNo" name="nar_orderNo" value="ORD_**************" />
	<input type="hidden" id="nar_merId" name="nar_merId" value="***************" /> -- (MID+SID)
	<input type="hidden" id="nar_txnCurrency" name="nar_txnCurrency" value="242" />
	<input type="hidden" id="nar_txnAmount" name="nar_txnAmount" value="30.00" />
	<input type="hidden" id="nar_remitterEmail" name="nar_remitterEmail" value="<EMAIL>" />
	<input type="hidden" id="nar_remitterMobile" name="nar_remitterMobile" value="**********" />
	<input type="hidden" id="nar_cardType" name="nar_cardType" value="EX" />
	<input
		type="hidden"
		id="nar_checkSum"
		name="nar_checkSum"
		value="2151CF6C22A3D0E035FEFAD70B52D77DB23F1ECDB4B45D8FB701F3DDE257664A3C232E09ABCF12E5D2D0C7A760AA63580DC9E68F61669DD10B53C3C662711D7682F5FFC4A9E0C0A9BC9121CBC9913200BB2F46FC1247D66B01F2E1AA76BE35CD5FB965AE7E998D334A0544FA1480FF18AA78C1D6ED2390CA3851AAB5DC75A36C6019588AF6F5948D446BAAD67FC904E3195D6B6F727A143C07A8995BF73DF1D9F977240B93BE6DF6AF5F74475EC7F9BCE54204A4AB47B2E30377F6F560BA0513925D84BCB00183FD6137E560B1732565A811B66690EEC2A461567239EB28A50AB8AC61255E0FAAF8454F578A0BDD5B49EDB757CAC69A14BC983DF67EAB118F07"
	/>
	<input type="hidden" id="nar_paymentDesc" name="nar_paymentDesc" value="TestPayment" />
	<input type="hidden" id="nar_version" name="nar_version" value="1.0" />
	<input type="hidden" id="nar_mcccode" name="nar_mcccode" value="4112" />
	<input
		type="hidden"
		id="nar_returnUrl"
		name="nar_returnUrl"
		value="https://uat2.yalamanchili.in/pgsim/checkresponse"
	/>
	<input type="hidden" id="nar_Secure" name="nar_Secure" value="IPGSECURE" /> -- RSA Encryption (Or)
	<input type="hidden" id="nar_Secure" name="nar_Secure" value="MERSECURE" /> -- HMAC SHA-256
	<input type="submit" value="Pay via NARADA® Secure" />
</form>
```

Please note Merchant Referral URL which is shared to BSP must be same in the HTTP header of Request URL from where the request is being sent because we validate this in the initial step.

#### 2.5.3 Checksum Calculation Method

The message level security is implemented by using signing and verification of the message.
Given below are the steps to sign a transaction request to NARADA® Secure:

**Step 1 – Construct the source string**

a. The source string should be formed with all data element values.
b. The values should then be sorted by their data element name, in ascending order.
c. Each element value should be separated by a "|" (pipe) character in between them.

Below is format of source string, in ascending order:

```
nar_cardType|nar_merBankCode|nar_merId|nar_merTxnTime|nar_msgType|nar_orderNo|nar_paymentDesc|nar_remitterEmail|nar_remitterMobile|nar_txnAmount|nar_txnCurrency|nar_version|nar_returnUrl
```

Below is sample of source string, constructed from the sample message in Section *******:

```
EX|01|***************|**************|AR|*******************|Sampleproductdescription|<EMAIL>|********|1.00|242|1.0|https://uat2.yalamanchili.in/pgsim/checkresponse
```

**Step 2 – Sign the source string**

a. Sign the constructed source string with Merchant's private key.
b. Take the signed value and populate it into the "nar_checkSum" data element.

### 2.6 Response Message

NARADA® Secure will send AC message to Merchant website. All messages are in Name / Value Pair (NVP) elements.

#### 2.6.1 AC Message

AC message is a response to AR and AS message.

In response to AR message, there will be AC messages sent by NARADA® Secure.

In response to AS message, NARADA® Secure will send AC message only. This message format is similar to response to AR message but it will be sent in one string. Merchant is required to decode this message – refer to Sample Source Code for more details.

AC message is the final acknowledgement message from NARADA® Secure. The message contains the transaction information from the bank, which inclusive of the Transaction Amount, and Debit Status.

**AC Message**
AC message will be sent after the remitter selects 'Click Here to Complete Your Transaction' button on NARADA® Secure Transaction status page.

### 2.7 Response Message Format – AC

#### 2.7.1 Message Format

Below are the response message formats of element for AC message:

| No  | Data Element       | Field Type | Data Type      | Length | Notes                                                                                                               |
| --- | ------------------ | ---------- | -------------- | ------ | ------------------------------------------------------------------------------------------------------------------- |
| 1   | nar_msgType        | M          | C              | 2      | Indicates message type. AC                                                                                          |
| 2   | nar_narTxnId       | M          | N              | 26     | Payment Id of the transaction generated by NARADA® Secure for each payment request received from remitter.          |
| 3   | nar_narTxnTime     | M          | D              | 14     | Date and time NARADA® Secure processes transaction. YYYYMMDDHH24MISS                                                |
| 4   | nar_merTxnTime     | M          | D              | 14     | Date and time of transaction originated from Merchant. YYYYMMDDHH24mmSS                                             |
| 5   | nar_orderNo        | M          | C              | 40     | Order number generated by Merchant.                                                                                 |
| 6   | nar_merId          | M          | C              | 15     | Merchant ID - provided by NARADA® Secure. E.g. ***************                                                      |
| 7   | nar_txnCurrency    | M          | C              | 3      | Transaction currency. Default value = 242(FJD)                                                                      |
| 8   | nar_txnAmount      | M          | N              | 16,2   | Total amount.                                                                                                       |
| 9   | nar_checkSum       | M          | C              | -      | Checksum value. Hold message signature.                                                                             |
| 10  | nar_remitterName   | O          | C, SC          | 40     | Remitter's name.                                                                                                    |
| 11  | nar_remitterBankId | O          | C              | 10     | Provided by NARADA® Secure – ties to the bank where remitter has an account in.                                     |
| 12  | nar_debitAuthCode  | M          | C              | 2      | Debit response code authorized by remitter Bank.                                                                    |
| 13  | nar_debitAuthNo    | O          | C, SC          | 6      | Mandatory if debit authorization code is '00'                                                                       |
| 14  | nar_cardType       | M          | VI/MC/AM/DC/EX |        | VI- Visa, MC-Mater Card, AM-American Express, DC- Dinners Club, EX –BSP Bank Cards                                  |
| 15  | nar_cardNo         | M          | C              | 19     | Masked Card No                                                                                                      |
| 16  | nar_remarks        | O          | C,SC           | 50     | Transaction remarks if any                                                                                          |
| 17  | nar_paymentId      | O          | C              | 26     | Payment Id of the transaction generated by NARADA® Secure for each approved payment request received from remitter. |

**Table 5 - Response Message Format AC**

#### 2.7.2 Sample Message

Below is the sample of NVP form element for response to AC message:

```
nar_msgType=AC
nar_narTxnId=83720101111391200427111391 nar_narTxnTime=**************
nar_merTxnTime=************** nar_orderNo=*******************
nar_merId=*************** nar_txnCurrency=242 nar_txnAmount=1.0
nar_checkSum=837CF44D341292342B16CED69A836CD72A52EDCAC14E9AB8F8A97502AED9695E91B006E63E88C97258D1C37B04AE0C2866DB911B648D6D2AEAAA96A188E64A9A3CACFEC0C8A948A9082D8EDC4A775F4B5FA17AF102816EF930B017A9613BD52D42162D2F27D4CC6005DBF2A1A32F60B9AE905EB745100B4789B8C4458792C6F0E07B53FA78707AA2D82C526B08FB971BCB4E711F5515E08E15C9D74AF5C8450FFEAA97CF10BA61AB0D2B13B300CBCFE8F6AA9F9897D6EB48568938E2F67EC286AC0636FD7FDE7E53C82C9E33A80FBE2B7BD587A93606E7397536CE702332FBF4752E47B02256152CD830D728CE84A457239B3862AEA1DECE4115C990C633D648
nar_remitterName=Test & User nar_remitterBankId=TEST0001
nar_debitAuthCode=00 nar_debitAuthNo=999999 nar_cardType=EX
nar_cardNo=4233XXXXXXXX1232 nar_remarks=Approved
nar_paymentId=83720101111391200427111391
```

Below is the sample of a full message for response to AC message:

```html
<form>
	<input type="hidden" name="nar_msgType" value="AC" />
	<input type="hidden" name="nar_narTxnId" value="83720101111391200427111391" />
	<input type="hidden" name="nar_narTxnTime" value="**************" />
	<input type="hidden" name="nar_merTxnTime" value="**************" />
	<input type="hidden" name="nar_orderNo" value="*******************" />
	<input type="hidden" name="nar_merId" value="***************" />
	<input type="hidden" name="nar_txnCurrency" value="242" />
	<input type="hidden" name="nar_txnAmount" value="1.0" />
	<input
		type="hidden"
		name="nar_checkSum"
		value="837CF44D341292342B16CED69A836CD72A52EDCAC14E9AB8F8A97502AED9695E91B006E63E88C97258D1C37B04AE0C2866DB911B648D6D2AEAAA96A188E64A9A3CACFEC0C8A948A9082D8EDC4A775F4B5FA17AF102816EF930B017A9613BD52D42162D2F27D4CC6005DBF2A1A32F60B9AE905EB745100B4789B8C4458792C6F0E07B53FA78707AA2D82C526B08FB971BCB4E711F5515E08E15C9D74AF5C8450FFEAA97CF10BA61AB0D2B13B300CBCFE8F6AA9F9897D6EB48568938E2F67EC286AC0636FD7FDE7E53C82C9E33A80FBE2B7BD587A93606E7397536CE702332FBF4752E47B02256152CD830D728CE84A457239B3862AEA1DECE4115C990C633D648"
	/>
	<input type="hidden" name="nar_remitterName" value="Test User" />
	<input type="hidden" name="nar_remitterBankId" value="TEST0001" />
	<input type="hidden" name="nar_debitAuthCode" value="00" />
	<input type="hidden" name="nar_debitAuthNo" value="999999" />
	<input type="hidden" name="nar_cardType" value="EX" />
	<input type="hidden" name="nar_cardNo" value="4233XXXXXXXX1232" />
	<input type="hidden" name="nar_remarks" value="Approved" />
	<input type="hidden" name="nar_paymentId" value="83720101111391200427111391 " />
</form>
```

Below is the sample of NVP form element for response to AS message (in one line):

```
nar_msgType=AC,nar_narTxnId=83720101111391200427111391,nar_narTxnTime=**************,nar_merTxnTime=**************,nar_orderNo=*******************,nar_merId=ME00000585,nar_txnCurrency=242,nar_txnAmount=1.0,nar_checkSum=837CF44D341292342B16CED69A836CD72A52EDCAC14E9AB8F8A97502AED9695E91B006E63E88C97258D1C37B04AE0C2866DB911B648D6D2AEAAA96A188E64A9A3CACFEC0C8A948A9082D8EDC4A775F4B5FA17AF102816EF930B017A9613BD52D42162D2F27D4CC6005DBF2A1A32F60B9AE905EB745100B4789B8C4458792C6F0E07B53FA78707AA2D82C526B08FB971BCB4E711F5515E08E15C9D74AF5C8450FFEAA97CF10BA61AB0D2B13B300CBCFE8F6AA9F9897D6EB48568938E2F67EC286AC0636FD7FDE7E53C82C9E33A80FBE2B7BD587A93606E7397536CE702332FBF4752E47B02256152CD830D728CE84A457239B3862AEA1DECE4115C990C633D648,nar_remitterName=Myclear&User,nar_remitterBankId=TEST0001,nar_debitAuthCode=00,nar_debitAuthNo=999999&nar_cardType=EX&nar_cardNo=4233XXXXXXXX1232&nar_remarks=Approved&nar_paymentId=83720101111391200427111391
```

Below is the sample of a full message response to AS message and the value is URL encoded:

```
nar_msgType=AC&nar_narTxnId=83720101111391200427111391&nar_narTxnTime=**************&nar_merTxnTime=**************&nar_orderNo=*******************&nar_merId=ME00000585&nar_txnCurrency=242&nar_txnAmount=1.0&nar_checkSum=837CF44D341292342B16CED69A836CD72A52EDCAC14E9AB8F8A97502AED9695E91B006E63E88C97258D1C37B04AE0C2866DB911B648D6D2AEAAA96A188E64A9A3CACFEC0C8A948A9082D8EDC4A775F4B5FA17AF102816EF930B017A9613BD52D42162D2F27D4CC6005DBF2A1A32F60B9AE905EB745100B4789B8C4458792C6F0E07B53FA78707AA2D82C526B08FB971BCB4E711F5515E08E15C9D74AF5C8450FFEAA97CF10BA61AB0D2B13B300CBCFE8F6AA9F9897D6EB48568938E2F67EC286AC0636FD7FDE7E53C82C9E33A80FBE2B7BD587A93606E7397536CE702332FBF4752E47B02256152CD830D728CE84A457239B3862AEA1DECE4115C990C633D648&nar_remitterName=Test%20%26%20User&nar_remitterBankId=TEST0001&nar_debitAuthCode=00&nar_debitAuthNo=999999&nar_cardType=EX&nar_cardNo=4233XXXXXXXX1232&nar_remarks=Approved&nar_paymentId=083720101111391200427111391
```

#### 2.7.2 Checksum Calculation Method

The message level security is implemented by using signing and verification of the message.
Given below are the steps to verify a transaction response from NARADA® Secure:

**Step 1 – Construct the source string**

a. The source string should be formed with all data element values received from NARADA® Secure.
b. The values should then be sorted by their data element name, in ascending order.
c. Each element value should be separated by a "|" (pipe) character in between them.
d. when there is any null in the Response then keep it as blank value in below Source string which is separated with '|, do not use the null value while constructing below string.

Below is format of source string, in ascending order:

```
nar_cardNo|nar_cardType|nar_debitAuthCode|nar_debitAuthNo|nar_merId|nar_merTxnTime|nar_msgType|nar_narTxnId|nar_narTxnTime|nar_orderNo|nar_remarks|nar_remitterBankId|nar_remitterName|nar_txnAmount|nar_txnCurrency
```

Below is the sample source string, which you have to construct from the sample message at Section2.7.2 received in

```
457817XXXXXX3227|EX|00|004502|***************|**************|AC|84270101118049210203118049|**************|ISP_5101527_**************|Approved|01|Test&User|1.00|242
```

**Step 2–Verify the source string**

Verify the constructed source string with data element "nar_checkSum" using NARADA® SECURE public key.

### 2.8 Response Codes

Response codes are used in AC message to show the status. The code is based on the status returned by the respective bank to NARADA® Secure.
Merchant should store the response code in their database for future transaction status verification and error debugging.

Below are the response codes used by NARADA® Secure:

| Response Code | Description                                  |
| ------------- | -------------------------------------------- |
| 00            | Approved                                     |
| 03            | Invalid Merchant                             |
| 05            | Do not honor                                 |
| 12            | Invalid Transaction                          |
| 13            | Invalid Amount                               |
| 14            | Invalid Customer Credentials                 |
| 20            | Invalid Response                             |
| 30            | Transaction Not Supported Or Format Error    |
| 45            | Duplicate Merchant Order Number              |
| 47            | Invalid Currency                             |
| 48            | Transaction Limit Exceeded                   |
| 51            | Insufficient Funds                           |
| 53            | No Savings Account                           |
| 57            | Transaction Not Permitted                    |
| 61            | Withdrawal Limit Exceeded                    |
| 65            | Withdrawal Frequency Exceeded                |
| 76            | Transaction Not Found                        |
| 78            | Checksum Decryption Failed                   |
| 80            | Buyer Cancel Transaction                     |
| 84            | Invalid Transaction Type                     |
| 85            | Internal Error At Bank System                |
| UC            | Transaction Cancelled By Customer            |
| UN            | Unknown error                                |
| TO            | Session Timeout at NARADA® Secure Entry Page |
| NF            | Txn Not Found                                |
| IM            | Invalid Message                              |
| 91            | Issuer Declined or Transaction Timed out     |
| N7            | Invalid CVV2                                 |

**Table 6 – Response Code**

---

## 3. Security Requirements using HMACSHA256

An alternative HMAC SHA-256 (Secure Hash Algorithm - 2) can be used since RSA public-key is a bit complex when compared to SHA-2 which requires less technical knowledge and less time for integration.

An Secret Key will be shared between the Merchant and Narada Secure which will be used by both parties for Checksum calculation and verification.

### 3.1.1 Sample Code in Java : To Sign/Verify Using HMACSHA256

```java
public static String signHashedRequest(String plainxml,String hKey) {
    String conhash=null;
    String hexstr =null;

    try {
        keyByte = hKey.getBytes("UTF-8");
        byte[] messageBytes = plainxml.getBytes("UTF-8");
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec keySpec = new SecretKeySpec(keyByte, "HmacSHA256");
        mac.init(keySpec);
        byte[] hashmessage = mac.doFinal(messageBytes);
        hexstr = Hex.encodeHexString(hashmessage).toLowerCase();
    } catch (Exception e) {
        log.debug("Exception in Sign Hash="+e);
    }
    return hexstr;
}
```

### 3.1.2 Sample Code in PHP : To Sign/Verify Using HMACSHA256

```php
public static String signHashedRequest($plainxml, $hKey) {
    try {
        $keyByte = utf8_encode($hKey);
        $messageBytes = utf8_encode($plainxml);

        $hashmessage = hash_hmac('sha256', $messageBytes, $keyByte);
        $hexstr = strtolower($hashmessage);

        return $hexstr;
    } catch (Exception $e) {
        error_log("Exception in Sign Hash: " . $e);
        return null; // or return an error code or message
    }
}
```

---

## 4. Security Requirements using PKI Keys

This document follows RSA public-key cryptosystems to check the integrity of the message between Merchant and NARADA® Secure.

### 4.1 SSL Certificate

Merchant should be responsible for their own SSL Certificate.

Merchant may use a trial certificate in their UAT environment. However, for production environment, Merchant is required to procure their SSL certificate from a third party Certificate Authority and other option to use self-signed certificate.

### 4.2 Merchant Certificate

Below are the steps required for Merchant to generate the private key and certificate:

#### 4.2.1 Procedure for Merchant Certificate Request in UAT Environment

The following flow is applicable for both first time certificate creation and certificate renewal:

a. Merchant should generate their own PKI key pair and ensure that the PKI private key is stored in a secure device. The PKI key pair can be generated using OpenSSL tool.

OpenSSL is compatible for Windows, Linux and Unix-based OS and can be obtained from the following site: http://www.openssl.org. Information on the "certificate generating utility" can be viewed at http://www.openssl.org/docs/apps/req.html. Refer to Open SSL Command for Multiplatform for more details.

Following are the openssl commands for generating key pair and certificate

• Generate Key o openssl genrsa -out <MID>\_UAT_pvt.pem 2048
• Generate CSR o openssl req -new -key <MID>\_UAT_pvt.pem -out <MID>.csr
• Self-Signed Certificate (Output Format is PEM) o openssl x509 -in <MID>.csr -out <MID>\_UAT_Certificate.pem -req -signkey <MID>\_UAT_pvt.pem -days 730

The CSR (Certificate Signing Request) file (<MID>.csr) needs to be submitted to a CA (Certifying Authority) for obtaining public CA signed certificate.

• PEM to DER – To Convert to DER format o openssl x509 -outform der -in <MID>\_UAT_Certificate.pem -out <MID>\_UAT_Certificate.der

Please share the file <MID>\_UAT_Certificate.der file with BSP. The PKI certificate is in .der format with 2048 bits key size while the signing algorithm is RSA.

The signed value is in hexadecimal format.

b. Merchant to procure their SSL certificate from third party Certificate Authority or use an self-signed certificate.

c. Certificate to be submitted to Narada@Secure

#### 4.2.2 Securing Private Keys & Certificates

All key files and certificates should be secured using any of the following methods:
a. Cryptographic storage such as Java Key Store (sample tools: Key Man, Key Tool), or Windows cryptographic storage. This storage is password protected.
b. A secured folder outside Web application path. This folder should only be accessible to the system administrator and Merchant's web application.
c. Any other method as guided by Merchant's company security policy.

#### 4.2.3 Certificate Validity

The validity for both merchants and NARADA® Secure certificates can be either 1 or 2 years. This duration applies to both Production and Testing environment.

**Merchant Certificate**

Merchant can renew the Merchant certificate prior to the expiry date.

Merchant is responsible for renewing the certificate, either by using their own program or manually replacing the certificate in a secured folder.

**NARADA® Secure Certificate**

Email reminder will be sent to the merchants prior to the certificate expiry date.
For Production certificate, reminder will be sent 1 month prior to the expiry date. For testing certificate, reminder will be sent one week prior to the expiry date.

Merchant has to get the latest certificate from NARADA® Secure and replace the certificate accordingly.

#### 4.2.4 Sample Code in Java: To Sign Using PKI Keys

```java
public String sign(PrivateKey prvkey,String data) throws Exception{
    Signature sig = Signature.getInstance("SHA1WithRSA");
    sig.initSign(prvkey); sig.update(data.getBytes()); byte[]
    signatureBytes = sig.sign();
    return byteArrayToHexString(signatureBytes);
}
public static String byteArrayToHexString(byte b[]) {
    StringBuffer sb = new StringBuffer(b.length* 2); for
    (int i = 0; i < b.length; i++) {
        sb.append(hexChar[(b[i] &0xf0) >>> 4]);
        sb.append(hexChar[b[i] & 0x0f]);
    }
    return sb.toString();
}
```

#### 4.2.5 Sample Code in Java : To Verify Using PKI Keys

```java
public boolean verify(PublicKey pubkey,String plaintext,byte[] signeddata) throws Exception{
    Signature sig = Signature.getInstance("SHA1withRSA");
    sig.initVerify(pubkey);
    sig.update(plaintext.getBytes());
    logger.debug("Value Byte ["+signeddata.length+"]");
    boolean status = sig.verify(signeddata);
    logger.debug("Verify Status: " +status); return
    status;
}
```

#### 4.2.6 Sample Code in PHP : To Sign Using PKI Keys

```php
$data = "EX|01|ME00000585|**************|AR|*******************|Sampleproductdescription|<EMAIL>|********|1.00|242|1.0|https://mpi.yalamanchili.in/pgsim/checkresponse";

$binary_signature = "";
$fp=fopen("private.key","r");
$priv_key=fread($fp,8192); fclose($fp);
$passphrase="1234"; //this will be the passphrase used to sign the key
$res = openssl_get_privatekey($priv_key,$passphrase);
openssl_sign($data, $binary_signature, $res, OPENSSL_ALGO_SHA1);
openssl_free_key($res); echo "Generate CheckSUM: ";
var_dump(bin2hex($binary_signature)); //Convert Binary Signature Value to HEX
```

#### 4.2.7 Sample Code in PHP : To Verify Using PKI Keys

```php
//Verify The Signed CheckSUM
$fpq=fopen ("ThirdPartyPublicKey.pem","r");
$pub_key=fread($fpq,8192); fclose($fpq);
$pubs = openssl_get_publickey($pub_key);
$ok = openssl_verify($data, $binary_signature, $pubs, OPENSSL_ALGO_SHA1); echo
"check #1: Verification ";
if ($ok == 1) {
    echo "signature ok (as it should be)\n";
} elseif ($ok == 0) {
    echo "bad (there's something wrong)\n";
} else {
    echo "ugly, error checking signature\n";
}
```

---

## Transaction Flow Diagrams

### Card Validation Failure

![Card Validation Failure Flow]

### Card Validation Success OTP Validation Failure

![Card Validation Success OTP Validation Failure Flow]

### Card Validation Success OTP Validation Success Issuer Authorisation Failure

![Card Validation Success OTP Validation Success Issuer Authorisation Failure Flow]

### Card Validation Success OTP Validation Success Issuer Authorisation Success

![Card Validation Success OTP Validation Success Issuer Authorisation Success Flow]

### Acquiring Scheme Card Validation Failure

![Acquiring Scheme Card Validation Failure Flow]

### Acquiring Scheme Card Validation Success OTP Validation Failure

![Acquiring Scheme Card Validation Success OTP Validation Failure Flow]

### Acquiring Scheme Card Validation Success OTP Validation Success Issuer Authorisation Failure

![Acquiring Scheme Card Validation Success OTP Validation Success Issuer Authorisation Failure Flow]

### Acquiring Scheme Card Validation Success OTP Validation Success Issuer Authorization Success

![Acquiring Scheme Card Validation Success OTP Validation Success Issuer Authorization Success Flow]

### VCC Card Validation Failure

![VCC Card Validation Failure Flow]

### VCC Card Validation Success OTP Validation Failure

![VCC Card Validation Success OTP Validation Failure Flow]

### VCC Card Validation Success OTP Validation Success Issuer Authorization Failure

![VCC Card Validation Success OTP Validation Success Issuer Authorization Failure Flow]

### VCC Card Validation Success OTP Validation Success Issuer Authorization Success

![VCC Card Validation Success OTP Validation Success Issuer Authorization Success Flow]

### VDC Card Validation Failure

![VDC Card Validation Failure Flow]

### VDC Card Validation Success OTP Validation Failure

![VDC Card Validation Success OTP Validation Failure Flow]

### VDC Card Validation Success OTP Validation Success Issuer Authorization Failure

![VDC Card Validation Success OTP Validation Success Issuer Authorization Failure Flow]

### VDC Card Validation Success OTP Validation Success Issuer Authorization Success

![VDC Card Validation Success OTP Validation Success Issuer Authorization Success Flow]

---

**Page 27 of 27**

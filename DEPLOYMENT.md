# Deployment Checklist for SPCA Fiji Donation Portal

## Pre-Deployment Checklist

### 1. BSP Credentials Verification
- [ ] Verify HMAC key is correct and active
- [ ] Confirm merchant ID, SID, and TID are accurate
- [ ] Test BSP gateway URL is accessible
- [ ] Verify MCC code and merchant name

### 2. Environment Configuration
- [ ] All required environment variables are set in Vercel
- [ ] Production URLs are correctly configured
- [ ] Return URL points to the correct endpoint
- [ ] Website address matches the deployed domain

### 3. Security Review
- [ ] HMAC signature generation is working correctly
- [ ] Rate limiting is configured appropriately
- [ ] Security headers are properly set
- [ ] Input validation is comprehensive
- [ ] Sensitive data is properly sanitized in logs

### 4. Testing
- [ ] Local development environment works
- [ ] Payment form validation works correctly
- [ ] BSP integration test passes
- [ ] Error handling works as expected
- [ ] Success and cancel pages display correctly

## Deployment Steps

### 1. Initial Setup
```bash
# Install Vercel CLI if not already installed
npm install -g vercel

# Login to Vercel
vercel login
```

### 2. Environment Variables Setup
Configure these in Vercel dashboard or via CLI:

```bash
# Set production environment variables
vercel env add BSP_HMAC_KEY production
vercel env add MERCHANT_ID production
vercel env add MERCHANT_SID production
vercel env add MERCHANT_TID production
vercel env add MERCHANT_NAME production
vercel env add MCC_CODE production
vercel env add BSP_PAYMENT_URL production
vercel env add RETURN_URL production
vercel env add WEBSITE_ADDRESS production
vercel env add NODE_ENV production
vercel env add LOG_LEVEL production
```

### 3. Deploy to Production
```bash
# Deploy to production
npm run deploy

# Or manually
vercel --prod
```

### 4. Post-Deployment Verification
- [ ] Visit the deployed URL and test the donation form
- [ ] Verify all static assets load correctly
- [ ] Test payment initiation (without completing payment)
- [ ] Check Vercel function logs for any errors
- [ ] Verify BSP callback URL is accessible

## Environment Variables Reference

| Variable | Example Value | Description |
|----------|---------------|-------------|
| `BSP_HMAC_KEY` | `abc123...` | HMAC key from BSP |
| `MERCHANT_ID` | `800126108001016` | BSP Merchant ID |
| `MERCHANT_SID` | `8001016` | BSP Merchant SID |
| `MERCHANT_TID` | `80011601` | BSP Merchant TID |
| `MERCHANT_NAME` | `SPCA Fiji` | Display name |
| `MCC_CODE` | `8398` | Merchant category code |
| `BSP_PAYMENT_URL` | `https://oceania.thecardservicesonline.com/bsppg/mercpg` | BSP gateway URL |
| `RETURN_URL` | `https://donations.spcafiji.com/api/payment-return` | Return endpoint |
| `WEBSITE_ADDRESS` | `https://donations.spcafiji.com` | Your domain |
| `NODE_ENV` | `production` | Environment |
| `LOG_LEVEL` | `info` | Logging level |

## Testing in Production

### 1. Basic Functionality Test
1. Visit the donation form
2. Fill in valid donation details
3. Submit the form
4. Verify redirect to BSP gateway
5. Cancel the payment and verify return to cancel page

### 2. BSP Integration Test
1. Use BSP test card numbers if available
2. Complete a test transaction
3. Verify callback handling
4. Check transaction logs

### 3. Error Handling Test
1. Submit invalid form data
2. Test rate limiting by making multiple requests
3. Verify error pages display correctly
4. Check security headers in browser dev tools

## Monitoring and Maintenance

### 1. Log Monitoring
- Monitor Vercel function logs for errors
- Set up alerts for payment failures
- Review security logs regularly

### 2. Performance Monitoring
- Monitor function execution times
- Check for any timeout issues
- Monitor memory usage

### 3. Security Monitoring
- Review rate limiting logs
- Monitor for suspicious IP activity
- Check HMAC verification failures

## Troubleshooting

### Common Issues

#### 1. HMAC Signature Mismatch
- Verify HMAC key is correct
- Check string-to-sign format
- Ensure character encoding is consistent

#### 2. Environment Variables Not Loading
- Verify variables are set in Vercel dashboard
- Check variable names match exactly
- Redeploy after adding new variables

#### 3. BSP Gateway Errors
- Verify merchant credentials
- Check BSP gateway URL
- Ensure return URL is accessible

#### 4. Function Timeouts
- Check function execution time
- Optimize code if necessary
- Increase timeout in vercel.json if needed

### Debug Commands
```bash
# View deployment logs
vercel logs

# List environment variables
vercel env ls

# View function details
vercel inspect [deployment-url]
```

## Rollback Procedure

If issues occur after deployment:

1. **Immediate Rollback**
   ```bash
   # Rollback to previous deployment
   vercel rollback [previous-deployment-url]
   ```

2. **Fix and Redeploy**
   - Fix the issue locally
   - Test thoroughly
   - Deploy again

3. **Emergency Contact**
   - BSP Support: [BSP contact information]
   - Vercel Support: <EMAIL>
   - SPCA Fiji: <EMAIL>

## Success Criteria

Deployment is successful when:
- [ ] Donation form loads without errors
- [ ] Payment initiation works correctly
- [ ] BSP integration functions properly
- [ ] All error handling works as expected
- [ ] Logs show no critical errors
- [ ] Security measures are active
- [ ] Performance is acceptable

## Post-Deployment Tasks

- [ ] Update DNS if necessary
- [ ] Notify stakeholders of successful deployment
- [ ] Schedule regular monitoring checks
- [ ] Document any deployment-specific configurations
- [ ] Update backup procedures if needed

{"version": 2, "builds": [{"src": "api/**/*.js", "use": "@vercel/node"}, {"src": "public/**/*", "use": "@vercel/static"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/public/$1"}], "env": {"BSP_HMAC_KEY": "@bsp_hmac_key", "BSP_MERCHANT_ID": "@bsp_merchant_id", "BSP_MERCHANT_PASSWORD": "@bsp_merchant_password", "MERCHANT_ID": "@merchant_id", "MERCHANT_SID": "@merchant_sid", "MERCHANT_TID": "@merchant_tid", "MCC_CODE": "@mcc_code", "MERCHANT_NAME": "@merchant_name", "BSP_PAYMENT_URL": "@bsp_payment_url", "BSP_PORTAL_URL": "@bsp_portal_url", "RETURN_URL": "@return_url", "WEBSITE_ADDRESS": "@website_address", "NODE_ENV": "@node_env", "SESSION_SECRET": "@session_secret", "ENCRYPTION_KEY": "@encryption_key"}, "functions": {"api/**/*.js": {"maxDuration": 30}}}
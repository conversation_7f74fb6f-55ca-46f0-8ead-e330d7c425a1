const logger = require('../lib/logger');

/**
 * API endpoint to serve payment redirect form
 * This endpoint serves the auto-submitting form that redirects to BSP gateway
 */
module.exports = async (req, res) => {
    try {
        const { orderId } = req.query;
        
        if (!orderId) {
            logger.warn('Payment redirect attempted without order ID');
            res.status(400).send(`
                <!DOCTYPE html>
                <html>
                <head><title>Payment Error</title></head>
                <body>
                    <h1>Payment Error</h1>
                    <p>Invalid payment request. Please try again.</p>
                    <a href="/">Return to donation form</a>
                </body>
                </html>
            `);
            return;
        }
        
        // Retrieve payment form from temporary storage
        const paymentForm = global.paymentForms?.[orderId];
        
        if (!paymentForm) {
            logger.warn('Payment form not found', { orderId });
            res.status(404).send(`
                <!DOCTYPE html>
                <html>
                <head><title>Payment Session Expired</title></head>
                <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                    <h1>Payment Session Expired</h1>
                    <p>Your payment session has expired. Please start a new donation.</p>
                    <a href="/" style="color: #667eea; text-decoration: none;">Return to donation form</a>
                </body>
                </html>
            `);
            return;
        }
        
        // Log payment redirect
        logger.info('Payment redirect served', {
            orderId,
            amount: paymentForm.bspData?.nar_txnAmount || paymentForm.data?.amount
        });

        // Set content type to HTML
        res.setHeader('Content-Type', 'text/html');

        // Serve the payment form (either new BSP format or legacy format)
        const formHtml = paymentForm.html || paymentForm.bspData;
        if (typeof formHtml === 'string') {
            res.status(200).send(formHtml);
        } else {
            // If we have BSP data object, create the form
            const bsp = new (require('../lib/bsp-integration'))();
            const html = bsp.createPaymentForm(formHtml);
            res.status(200).send(html);
        }
        
        // Clean up the form after serving (one-time use)
        delete global.paymentForms[orderId];
        
    } catch (error) {
        logger.error('Payment redirect error', { 
            error: error.message, 
            stack: error.stack 
        });
        
        res.status(500).send(`
            <!DOCTYPE html>
            <html>
            <head><title>Payment Error</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
                <h1>Payment Error</h1>
                <p>An error occurred while processing your payment request.</p>
                <p>Please try again or contact support if the problem persists.</p>
                <a href="/" style="color: #667eea; text-decoration: none;">Return to donation form</a>
            </body>
            </html>
        `);
    }
};

const BSPIntegration = require('../lib/bsp-integration');
const logger = require('../lib/logger');
const security = require('../lib/security');

/**
 * API endpoint to handle BSP payment gateway callbacks
 * This endpoint receives notifications from BSP about payment status
 */
module.exports = async (req, res) => {
    try {
        // Apply security headers
        security.securityHeaders(req, res, () => {});

        const clientIP = security.getClientIP(req);

        // Log callback received
        logger.info('Payment callback received', {
            method: req.method,
            ip: clientIP,
            userAgent: req.headers['user-agent']
        });
        
        // BSP typically sends callbacks via POST
        if (req.method !== 'POST') {
            logger.warn('Invalid callback method', { method: req.method });
            res.status(405).json({ error: 'Method not allowed' });
            return;
        }
        
        const callbackData = req.body;
        
        // Log callback data (sanitized)
        logger.info('Callback data received', { callbackData });
        
        // Initialize BSP integration
        const bsp = new BSPIntegration();
        
        // Verify callback signature
        const isValidSignature = bsp.verifyCallback(callbackData);
        
        if (!isValidSignature) {
            logger.logSecurity('Invalid callback signature', {
                ip: clientIP,
                userAgent: req.headers['user-agent'],
                details: 'BSP callback signature verification failed'
            });
            
            res.status(400).json({ error: 'Invalid signature' });
            return;
        }
        
        // Parse payment status
        const paymentStatus = bsp.parsePaymentStatus(callbackData);
        
        // Log transaction status
        logger.logTransaction('payment_callback', paymentStatus);
        
        // Process payment based on status
        await processPaymentCallback(paymentStatus, callbackData);
        
        // Respond to BSP (they expect a specific response format)
        res.status(200).json({
            status: 'OK',
            message: 'Callback processed successfully',
            orderId: paymentStatus.orderId
        });
        
    } catch (error) {
        logger.error('Payment callback processing error', { 
            error: error.message, 
            stack: error.stack,
            body: req.body
        });
        
        // Still respond OK to prevent BSP from retrying
        res.status(200).json({
            status: 'ERROR',
            message: 'Callback processing failed'
        });
    }
};

/**
 * Process payment callback based on status
 * @param {Object} paymentStatus - Parsed payment status
 * @param {Object} rawData - Raw callback data
 */
async function processPaymentCallback(paymentStatus, rawData) {
    try {
        switch (paymentStatus.status) {
            case 'success':
                await handleSuccessfulPayment(paymentStatus, rawData);
                break;
                
            case 'failed':
                await handleFailedPayment(paymentStatus, rawData);
                break;
                
            case 'pending':
                await handlePendingPayment(paymentStatus, rawData);
                break;
                
            default:
                logger.warn('Unknown payment status', { status: paymentStatus.status });
                break;
        }
    } catch (error) {
        logger.error('Payment callback processing error', { 
            error: error.message,
            paymentStatus 
        });
        throw error;
    }
}

/**
 * Handle successful payment
 * @param {Object} paymentStatus - Payment status
 * @param {Object} rawData - Raw callback data
 */
async function handleSuccessfulPayment(paymentStatus, rawData) {
    logger.info('Processing successful payment', { 
        orderId: paymentStatus.orderId,
        amount: paymentStatus.amount,
        transactionId: paymentStatus.transactionId
    });
    
    // In a real application, you would:
    // 1. Update database with payment confirmation
    // 2. Send confirmation email to donor
    // 3. Update donation records
    // 4. Trigger any post-payment workflows
    
    // For this test integration, we'll just log the success
    logger.logTransaction('payment_success', paymentStatus);
    
    // Store successful payment (in production, use database)
    global.paymentResults = global.paymentResults || {};
    global.paymentResults[paymentStatus.orderId] = {
        status: 'success',
        data: paymentStatus,
        timestamp: new Date().toISOString()
    };
}

/**
 * Handle failed payment
 * @param {Object} paymentStatus - Payment status
 * @param {Object} rawData - Raw callback data
 */
async function handleFailedPayment(paymentStatus, rawData) {
    logger.warn('Processing failed payment', { 
        orderId: paymentStatus.orderId,
        message: paymentStatus.message
    });
    
    // In a real application, you would:
    // 1. Update database with failure reason
    // 2. Send failure notification if needed
    // 3. Log for analysis
    
    logger.logTransaction('payment_failed', paymentStatus);
    
    // Store failed payment result
    global.paymentResults = global.paymentResults || {};
    global.paymentResults[paymentStatus.orderId] = {
        status: 'failed',
        data: paymentStatus,
        timestamp: new Date().toISOString()
    };
}

/**
 * Handle pending payment
 * @param {Object} paymentStatus - Payment status
 * @param {Object} rawData - Raw callback data
 */
async function handlePendingPayment(paymentStatus, rawData) {
    logger.info('Processing pending payment', { 
        orderId: paymentStatus.orderId
    });
    
    // In a real application, you would:
    // 1. Update database with pending status
    // 2. Set up monitoring for final status
    // 3. Possibly send pending notification
    
    logger.logTransaction('payment_pending', paymentStatus);
    
    // Store pending payment result
    global.paymentResults = global.paymentResults || {};
    global.paymentResults[paymentStatus.orderId] = {
        status: 'pending',
        data: paymentStatus,
        timestamp: new Date().toISOString()
    };
}

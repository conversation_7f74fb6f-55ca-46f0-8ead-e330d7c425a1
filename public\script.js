// DOM Elements
const donationForm = document.getElementById('donationForm');
const amountButtons = document.querySelectorAll('.amount-btn');
const customAmountInput = document.getElementById('customAmount');
const donateBtn = document.getElementById('donateBtn');
const btnText = document.querySelector('.btn-text');
const btnLoader = document.querySelector('.btn-loader');

// Amount button handling
amountButtons.forEach(button => {
    button.addEventListener('click', (e) => {
        e.preventDefault();
        
        // Remove active class from all buttons
        amountButtons.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to clicked button
        button.classList.add('active');
        
        // Set the amount in the custom input
        const amount = button.dataset.amount;
        customAmountInput.value = amount;
        
        // Trigger validation
        validateAmount();
    });
});

// Custom amount input handling
customAmountInput.addEventListener('input', () => {
    // Remove active class from all preset buttons
    amountButtons.forEach(btn => btn.classList.remove('active'));
    
    // Validate amount
    validateAmount();
});

// Amount validation
function validateAmount() {
    const amount = parseFloat(customAmountInput.value);
    const isValid = amount >= 5 && amount <= 10000;
    
    if (customAmountInput.value && !isValid) {
        customAmountInput.setCustomValidity('Amount must be between $5 and $10,000');
    } else {
        customAmountInput.setCustomValidity('');
    }
    
    return isValid;
}

// Form validation
function validateForm() {
    const requiredFields = [
        'amount',
        'firstName', 
        'lastName',
        'email',
        'terms'
    ];
    
    let isValid = true;
    const errors = [];
    
    // Check required fields
    requiredFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        if (field.type === 'checkbox') {
            if (!field.checked) {
                isValid = false;
                errors.push(`${fieldName} is required`);
            }
        } else if (!field.value.trim()) {
            isValid = false;
            errors.push(`${fieldName} is required`);
        }
    });
    
    // Validate email format
    const email = document.querySelector('[name="email"]').value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (email && !emailRegex.test(email)) {
        isValid = false;
        errors.push('Please enter a valid email address');
    }
    
    // Validate amount
    if (!validateAmount()) {
        isValid = false;
        errors.push('Please enter a valid donation amount');
    }
    
    return { isValid, errors };
}

// Show loading state
function setLoadingState(loading) {
    if (loading) {
        donateBtn.disabled = true;
        btnText.style.display = 'none';
        btnLoader.style.display = 'inline-block';
    } else {
        donateBtn.disabled = false;
        btnText.style.display = 'inline-block';
        btnLoader.style.display = 'none';
    }
}

// Show error message
function showError(message) {
    // Remove existing error messages
    const existingError = document.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Create and show new error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        background: #fed7d7;
        color: #c53030;
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        border: 1px solid #feb2b2;
    `;
    errorDiv.textContent = message;
    
    donationForm.insertBefore(errorDiv, donateBtn);
    
    // Scroll to error message
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Show success message
function showSuccess(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        background: #c6f6d5;
        color: #2f855a;
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
        border: 1px solid #9ae6b4;
    `;
    successDiv.textContent = message;
    
    donationForm.insertBefore(successDiv, donateBtn);
}

// Collect form data
function collectFormData() {
    const formData = new FormData(donationForm);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // Add additional metadata
    data.timestamp = new Date().toISOString();
    data.userAgent = navigator.userAgent;
    data.referrer = document.referrer;
    
    return data;
}

// Submit form to payment gateway
async function submitPayment(formData) {
    try {
        const response = await fetch('/api/initiate-payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || 'Payment initiation failed');
        }
        
        if (result.success && result.redirectUrl) {
            // Redirect to BSP payment gateway
            window.location.href = result.redirectUrl;
        } else {
            throw new Error(result.error || 'Invalid response from payment service');
        }
        
    } catch (error) {
        console.error('Payment submission error:', error);
        throw error;
    }
}

// Form submission handler
donationForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.error-message, .success-message');
    existingMessages.forEach(msg => msg.remove());
    
    // Validate form
    const validation = validateForm();
    if (!validation.isValid) {
        showError(validation.errors[0]);
        return;
    }
    
    // Set loading state
    setLoadingState(true);
    
    try {
        // Collect form data
        const formData = collectFormData();
        
        // Submit to payment gateway
        await submitPayment(formData);
        
    } catch (error) {
        setLoadingState(false);
        showError(error.message || 'An error occurred while processing your donation. Please try again.');
    }
});

// Initialize form
document.addEventListener('DOMContentLoaded', () => {
    // Set default amount if none selected
    if (!customAmountInput.value) {
        customAmountInput.value = '50';
        amountButtons[1]?.classList.add('active'); // $50 button
    }
    
    // Add input event listeners for real-time validation
    const inputs = donationForm.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', () => {
            // Remove error styling on focus out if field is now valid
            if (input.checkValidity()) {
                input.style.borderColor = '#e2e8f0';
            }
        });
        
        input.addEventListener('invalid', () => {
            // Add error styling for invalid fields
            input.style.borderColor = '#e53e3e';
        });
    });
});

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-FJ', {
        style: 'currency',
        currency: 'FJD',
        minimumFractionDigits: 2
    }).format(amount);
}

// Update button text with amount
customAmountInput.addEventListener('input', () => {
    const amount = parseFloat(customAmountInput.value);
    if (amount && amount >= 5) {
        btnText.textContent = `Donate ${formatCurrency(amount)}`;
    } else {
        btnText.textContent = 'Proceed to Payment';
    }
});

// Handle browser back button
window.addEventListener('pageshow', (event) => {
    if (event.persisted) {
        // Page was loaded from cache (user pressed back button)
        setLoadingState(false);
    }
});

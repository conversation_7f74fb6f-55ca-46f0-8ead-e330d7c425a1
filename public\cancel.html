<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Cancelled - SPCA Fiji</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .cancel-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px 20px;
            text-align: center;
        }
        
        .cancel-card {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .cancel-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 40px;
            color: white;
        }
        
        .cancel-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .cancel-subtitle {
            font-size: 1.2rem;
            color: #718096;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .info-box {
            background: #fff5f5;
            border: 1px solid #feb2b2;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .info-box h3 {
            color: #c53030;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .info-box p {
            color: #2d3748;
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .help-section {
            background: #f7fafc;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .help-section h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .help-section ul {
            list-style: none;
            padding: 0;
        }
        
        .help-section li {
            padding: 8px 0;
            color: #4a5568;
            position: relative;
            padding-left: 25px;
        }
        
        .help-section li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }
        
        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }
        
        .contact-info {
            background: #ebf8ff;
            border: 1px solid #bee3f8;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            text-align: left;
        }
        
        .contact-info h3 {
            color: #2b6cb0;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        
        .contact-info p {
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .contact-info a {
            color: #2b6cb0;
            text-decoration: none;
        }
        
        .contact-info a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .cancel-card {
                padding: 25px;
            }
            
            .cancel-title {
                font-size: 2rem;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <h1>SPCA Fiji</h1>
                <p>Society for the Prevention of Cruelty to Animals</p>
            </div>
        </header>

        <main class="cancel-container">
            <div class="cancel-card">
                <div class="cancel-icon">
                    ✕
                </div>
                
                <h1 class="cancel-title">Payment Cancelled</h1>
                <p class="cancel-subtitle">
                    Your payment was cancelled and no charges were made to your account.
                </p>
                
                <div class="info-box">
                    <h3>What happened?</h3>
                    <p>Your payment was cancelled either by your request or due to a technical issue during the payment process.</p>
                    <p>No money has been charged to your payment method, and your donation was not processed.</p>
                </div>
                
                <div class="help-section">
                    <h3>Common reasons for payment cancellation:</h3>
                    <ul>
                        <li>You clicked the "Cancel" or "Back" button during payment</li>
                        <li>Your browser session timed out</li>
                        <li>There was a temporary connection issue</li>
                        <li>Your payment method was declined by your bank</li>
                        <li>You closed the payment window before completing the transaction</li>
                    </ul>
                </div>
                
                <div class="action-buttons">
                    <a href="/" class="btn btn-primary">Try Again</a>
                    <a href="https://spcafiji.com/contact" class="btn btn-secondary" target="_blank">Contact Us</a>
                </div>
                
                <div class="contact-info">
                    <h3>Need Help?</h3>
                    <p>If you're experiencing technical difficulties or have questions about making a donation, please don't hesitate to contact us:</p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p><strong>Phone:</strong> <a href="tel:+**********">+************</a></p>
                    <p><strong>Office Hours:</strong> Monday - Friday, 8:00 AM - 5:00 PM (Fiji Time)</p>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 SPCA Fiji. All rights reserved. | We're here to help!</p>
        </footer>
    </div>

    <script>
        // Log cancellation for analytics
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const orderId = urlParams.get('orderId');
            const reason = urlParams.get('reason');
            
            // Log cancellation event (in production, send to analytics)
            console.log('Payment cancelled:', {
                orderId: orderId,
                reason: reason,
                timestamp: new Date().toISOString()
            });
            
            // Show specific reason if provided
            if (reason) {
                const infoBox = document.querySelector('.info-box p:last-child');
                switch(reason) {
                    case 'user_cancelled':
                        infoBox.textContent = 'You cancelled the payment process.';
                        break;
                    case 'timeout':
                        infoBox.textContent = 'The payment session timed out due to inactivity.';
                        break;
                    case 'declined':
                        infoBox.textContent = 'Your payment method was declined. Please try a different payment method.';
                        break;
                    case 'error':
                        infoBox.textContent = 'A technical error occurred during payment processing.';
                        break;
                    default:
                        // Keep default message
                        break;
                }
            }
        });
        
        // Auto-focus on try again button for better UX
        setTimeout(() => {
            const tryAgainBtn = document.querySelector('.btn-primary');
            if (tryAgainBtn) {
                tryAgainBtn.focus();
            }
        }, 1000);
    </script>
</body>
</html>

const crypto = require('crypto');
const validator = require('validator');
const logger = require('./logger');

/**
 * Security utilities for BSP payment integration
 */
class Security {
    constructor() {
        this.rateLimitStore = new Map();
        this.suspiciousIPs = new Set();
        this.maxRequestsPerMinute = 10;
        this.maxRequestsPerHour = 100;
    }
    
    /**
     * Rate limiting middleware
     * @param {Object} req - Request object
     * @param {Object} res - Response object
     * @param {Function} next - Next middleware function
     */
    rateLimit(req, res, next) {
        const ip = this.getClientIP(req);
        const now = Date.now();
        const minute = Math.floor(now / 60000);
        const hour = Math.floor(now / 3600000);
        
        const key = `${ip}:${minute}`;
        const hourKey = `${ip}:${hour}`;
        
        // Clean old entries
        this.cleanupRateLimit();
        
        // Check minute limit
        const minuteCount = this.rateLimitStore.get(key) || 0;
        if (minuteCount >= this.maxRequestsPerMinute) {
            logger.logSecurity('Rate limit exceeded (minute)', {
                ip,
                count: minuteCount,
                limit: this.maxRequestsPerMinute
            });
            
            res.status(429).json({
                error: 'Too many requests',
                message: 'Please wait before making another request'
            });
            return;
        }
        
        // Check hour limit
        const hourCount = this.getHourlyCount(ip, hour);
        if (hourCount >= this.maxRequestsPerHour) {
            logger.logSecurity('Rate limit exceeded (hour)', {
                ip,
                count: hourCount,
                limit: this.maxRequestsPerHour
            });
            
            this.suspiciousIPs.add(ip);
            
            res.status(429).json({
                error: 'Too many requests',
                message: 'Hourly limit exceeded. Please try again later.'
            });
            return;
        }
        
        // Update counters
        this.rateLimitStore.set(key, minuteCount + 1);
        
        next();
    }
    
    /**
     * Get client IP address
     * @param {Object} req - Request object
     * @returns {string} Client IP
     */
    getClientIP(req) {
        return req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
               req.headers['x-real-ip'] ||
               req.connection.remoteAddress ||
               req.socket.remoteAddress ||
               'unknown';
    }
    
    /**
     * Get hourly request count for IP
     * @param {string} ip - IP address
     * @param {number} hour - Hour timestamp
     * @returns {number} Request count
     */
    getHourlyCount(ip, hour) {
        let count = 0;
        for (let [key, value] of this.rateLimitStore.entries()) {
            if (key.startsWith(`${ip}:`) && key.includes(hour.toString())) {
                count += value;
            }
        }
        return count;
    }
    
    /**
     * Clean up old rate limit entries
     */
    cleanupRateLimit() {
        const now = Date.now();
        const cutoff = Math.floor((now - 3600000) / 60000); // 1 hour ago
        
        for (let [key] of this.rateLimitStore.entries()) {
            const timestamp = parseInt(key.split(':')[1]);
            if (timestamp < cutoff) {
                this.rateLimitStore.delete(key);
            }
        }
    }
    
    /**
     * Validate and sanitize payment data
     * @param {Object} data - Payment data
     * @returns {Object} Validation result
     */
    validatePaymentData(data) {
        const errors = [];
        const sanitized = {};
        
        // Required fields validation
        const requiredFields = {
            amount: 'number',
            firstName: 'string',
            lastName: 'string',
            email: 'email'
        };
        
        for (const [field, type] of Object.entries(requiredFields)) {
            if (!data[field]) {
                errors.push(`${field} is required`);
                continue;
            }
            
            switch (type) {
                case 'number':
                    const num = parseFloat(data[field]);
                    if (isNaN(num) || num <= 0) {
                        errors.push(`${field} must be a valid positive number`);
                    } else if (field === 'amount' && (num < 5 || num > 10000)) {
                        errors.push('Amount must be between $5 and $10,000');
                    } else {
                        sanitized[field] = num;
                    }
                    break;
                    
                case 'string':
                    const str = this.sanitizeString(data[field]);
                    if (!str || str.length < 2) {
                        errors.push(`${field} must be at least 2 characters`);
                    } else if (str.length > 50) {
                        errors.push(`${field} must be less than 50 characters`);
                    } else {
                        sanitized[field] = str;
                    }
                    break;
                    
                case 'email':
                    if (!validator.isEmail(data[field])) {
                        errors.push(`${field} must be a valid email address`);
                    } else {
                        sanitized[field] = validator.normalizeEmail(data[field]);
                    }
                    break;
            }
        }
        
        // Optional fields validation
        const optionalFields = {
            phone: 'phone',
            city: 'string',
            country: 'string',
            purpose: 'string',
            message: 'text'
        };
        
        for (const [field, type] of Object.entries(optionalFields)) {
            if (data[field]) {
                switch (type) {
                    case 'phone':
                        if (validator.isMobilePhone(data[field], 'any', { strictMode: false })) {
                            sanitized[field] = data[field];
                        }
                        break;
                        
                    case 'string':
                        const str = this.sanitizeString(data[field]);
                        if (str && str.length <= 100) {
                            sanitized[field] = str;
                        }
                        break;
                        
                    case 'text':
                        const text = this.sanitizeString(data[field]);
                        if (text && text.length <= 500) {
                            sanitized[field] = text;
                        }
                        break;
                }
            }
        }
        
        // Boolean fields
        sanitized.terms = data.terms === 'on' || data.terms === true;
        sanitized.newsletter = data.newsletter === 'on' || data.newsletter === true;
        
        if (!sanitized.terms) {
            errors.push('You must accept the terms and conditions');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            data: sanitized
        };
    }
    
    /**
     * Sanitize string input
     * @param {string} input - Input string
     * @returns {string} Sanitized string
     */
    sanitizeString(input) {
        if (typeof input !== 'string') return '';
        
        return input
            .trim()
            .replace(/[<>]/g, '') // Remove potential HTML tags
            .replace(/[^\w\s\-\.@]/g, '') // Allow only alphanumeric, spaces, hyphens, dots, @
            .substring(0, 255); // Limit length
    }
    
    /**
     * Generate secure random token
     * @param {number} length - Token length
     * @returns {string} Random token
     */
    generateSecureToken(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }
    
    /**
     * Hash sensitive data
     * @param {string} data - Data to hash
     * @returns {string} Hashed data
     */
    hashData(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }
    
    /**
     * Encrypt sensitive data
     * @param {string} data - Data to encrypt
     * @param {string} key - Encryption key
     * @returns {string} Encrypted data
     */
    encryptData(data, key) {
        const algorithm = 'aes-256-gcm';
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(algorithm, key);
        
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const authTag = cipher.getAuthTag();
        
        return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
    }
    
    /**
     * Decrypt sensitive data
     * @param {string} encryptedData - Encrypted data
     * @param {string} key - Decryption key
     * @returns {string} Decrypted data
     */
    decryptData(encryptedData, key) {
        const algorithm = 'aes-256-gcm';
        const parts = encryptedData.split(':');
        
        if (parts.length !== 3) {
            throw new Error('Invalid encrypted data format');
        }
        
        const iv = Buffer.from(parts[0], 'hex');
        const authTag = Buffer.from(parts[1], 'hex');
        const encrypted = parts[2];
        
        const decipher = crypto.createDecipher(algorithm, key);
        decipher.setAuthTag(authTag);
        
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }
    
    /**
     * Check if IP is suspicious
     * @param {string} ip - IP address
     * @returns {boolean} Is suspicious
     */
    isSuspiciousIP(ip) {
        return this.suspiciousIPs.has(ip);
    }
    
    /**
     * Security headers middleware
     * @param {Object} req - Request object
     * @param {Object} res - Response object
     * @param {Function} next - Next middleware function
     */
    securityHeaders(req, res, next) {
        // Set security headers
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');
        res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com");
        
        // Remove server information
        res.removeHeader('X-Powered-By');
        
        next();
    }
}

// Create singleton instance
const security = new Security();

module.exports = security;

const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * BSP IPG Integration Library
 * Handles payment gateway integration with BSP Bank
 */
class BSPIntegration {
    constructor() {
        this.config = {
            hmacKey: process.env.BSP_HMAC_KEY,
            merchantId: process.env.MERCHANT_ID,
            merchantSid: process.env.MERCHANT_SID,
            merchantTid: process.env.MERCHANT_TID,
            mccCode: process.env.MCC_CODE,
            merchantName: process.env.MERCHANT_NAME,
            paymentUrl: process.env.BSP_PAYMENT_URL,
            returnUrl: process.env.RETURN_URL,
            websiteAddress: process.env.WEBSITE_ADDRESS
        };
        
        this.validateConfig();
    }
    
    /**
     * Validate required configuration
     */
    validateConfig() {
        const required = ['hmacKey', 'merchantId', 'merchantSid', 'merchantTid', 'paymentUrl', 'returnUrl'];
        const missing = required.filter(key => !this.config[key]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required BSP configuration: ${missing.join(', ')}`);
        }
    }
    
    /**
     * Generate HMAC signature for BSP requests
     * @param {Object} data - Payment data
     * @returns {string} HMAC signature
     */
    generateHMAC(data) {
        try {
            // Create the string to sign based on BSP documentation
            const stringToSign = this.createStringToSign(data);

            // Generate HMAC-SHA256 signature (lowercase for BSP requests)
            const hmac = crypto.createHmac('sha256', this.config.hmacKey);
            hmac.update(stringToSign, 'utf8');
            const signature = hmac.digest('hex').toLowerCase();

            console.log('BSP Request HMAC Generation:', {
                stringToSign,
                signature: signature.substring(0, 10) + '...' // Log partial signature for debugging
            });

            return signature;
        } catch (error) {
            console.error('HMAC generation error:', error);
            throw new Error('Failed to generate payment signature');
        }
    }
    
    /**
     * Create string to sign for HMAC generation according to BSP IPG documentation
     * @param {Object} data - Payment data with BSP field names
     * @returns {string} String to sign
     */
    createStringToSign(data) {
        // BSP IPG documentation specifies exact order for checksum calculation:
        // nar_cardType|nar_merBankCode|nar_merId|nar_merTxnTime|nar_msgType|nar_orderNo|nar_paymentDesc|nar_remitterEmail|nar_remitterMobile|nar_txnAmount|nar_txnCurrency|nar_version|nar_returnUrl
        const fields = [
            data.nar_cardType || 'EX',  // EX for BSP Bank Cards
            data.nar_merBankCode || '01',  // Default value = 01
            data.nar_merId,
            data.nar_merTxnTime,
            data.nar_msgType,
            data.nar_orderNo,
            data.nar_paymentDesc,
            data.nar_remitterEmail || '',
            data.nar_remitterMobile || '',
            data.nar_txnAmount,
            data.nar_txnCurrency,
            data.nar_version,
            data.nar_returnUrl
        ];

        return fields.join('|');
    }
    
    /**
     * Prepare payment request data for BSP gateway according to BSP IPG documentation
     * @param {Object} donationData - Donation form data
     * @returns {Object} BSP payment request data with correct field names
     */
    preparePaymentRequest(donationData) {
        try {
            const orderId = this.generateOrderId();
            const merTxnTime = this.formatBSPTimestamp(new Date());
            const amount = this.formatBSPAmount(donationData.amount);

            // BSP IPG required fields with exact field names from documentation
            const paymentData = {
                // Required fields according to BSP IPG Table 4
                nar_msgType: 'AR',  // Authorization Request
                nar_merTxnTime: merTxnTime,  // YYYYMMDDHH24MISS format
                nar_orderNo: orderId,  // Unique order number
                nar_merId: this.config.merchantId,  // MID+SID format
                nar_merBankCode: '01',  // Default value = 01
                nar_txnCurrency: '242',  // FJD currency code
                nar_txnAmount: amount,  // Decimal format (e.g., "30.00")
                nar_remitterEmail: donationData.email || '',  // Optional
                nar_remitterMobile: donationData.phone ? donationData.phone.replace(/\D/g, '') : '',  // Optional, numbers only
                nar_cardType: 'EX',  // EX for BSP Bank Cards
                nar_paymentDesc: `SPCA Fiji Donation - ${donationData.purpose || 'General Fund'}`.substring(0, 30),  // Max 30 chars
                nar_version: '1.0',  // Default version
                nar_returnUrl: this.config.returnUrl,  // Return URL
                nar_mcccode: this.config.mccCode,  // Merchant Category Code
                nar_Secure: 'MERSECURE'  // HMAC SHA-256 algorithm
            };

            // Generate HMAC signature using BSP format
            paymentData.nar_checkSum = this.generateHMAC(paymentData);

            // Store original data for reference
            global.paymentForms = global.paymentForms || {};
            global.paymentForms[orderId] = {
                originalData: donationData,
                bspData: paymentData,
                created: Date.now()
            };

            return paymentData;

        } catch (error) {
            console.error('Payment request preparation error:', error);
            throw new Error('Failed to prepare payment request');
        }
    }
    
    /**
     * Generate unique order ID
     * @returns {string} Order ID
     */
    generateOrderId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8).toUpperCase();
        return `SPCA${timestamp}${random}`;
    }
    
    /**
     * Format amount for BSP gateway (decimal format as per BSP documentation)
     * @param {number} amount - Amount in dollars
     * @returns {string} Formatted amount (e.g., "30.00")
     */
    formatBSPAmount(amount) {
        const numAmount = parseFloat(amount);
        if (isNaN(numAmount) || numAmount <= 0) {
            throw new Error('Invalid amount');
        }
        return numAmount.toFixed(2);
    }

    /**
     * Format timestamp for BSP gateway (YYYYMMDDHH24MISS format)
     * @param {Date} date - Date object
     * @returns {string} Formatted timestamp
     */
    formatBSPTimestamp(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}${month}${day}${hours}${minutes}${seconds}`;
    }
    
    /**
     * Create payment form HTML for auto-submission to BSP
     * @param {Object} paymentData - Payment request data
     * @returns {string} HTML form
     */
    createPaymentForm(paymentData) {
        const formFields = Object.entries(paymentData)
            .map(([key, value]) => `<input type="hidden" name="${key}" value="${this.escapeHtml(value)}">`)
            .join('\n');
        
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Redirecting to Payment Gateway...</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 50px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                    }
                    .loader { 
                        border: 4px solid #f3f3f3; 
                        border-top: 4px solid #667eea; 
                        border-radius: 50%; 
                        width: 40px; 
                        height: 40px; 
                        animation: spin 1s linear infinite; 
                        margin: 20px auto;
                    }
                    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                </style>
            </head>
            <body>
                <h2>Redirecting to Secure Payment Gateway...</h2>
                <div class="loader"></div>
                <p>Please wait while we redirect you to BSP Bank's secure payment page.</p>
                <p>If you are not redirected automatically, <a href="#" onclick="document.forms[0].submit();" style="color: #fff;">click here</a>.</p>
                
                <form method="POST" action="${this.config.paymentUrl}" id="paymentForm">
                    ${formFields}
                </form>
                
                <script>
                    // Auto-submit form after a short delay
                    setTimeout(function() {
                        document.getElementById('paymentForm').submit();
                    }, 2000);
                </script>
            </body>
            </html>
        `;
    }
    
    /**
     * Verify HMAC signature from BSP callback according to BSP IPG documentation
     * @param {Object} callbackData - Data from BSP callback with nar_ prefixed fields
     * @returns {boolean} Signature is valid
     */
    verifyCallback(callbackData) {
        try {
            const receivedSignature = callbackData.nar_checkSum;
            if (!receivedSignature) {
                console.error('No nar_checkSum in callback data');
                return false;
            }

            // Create verification data without checksum
            const dataForVerification = { ...callbackData };
            delete dataForVerification.nar_checkSum;

            // Generate expected signature using BSP response format
            const expectedSignature = this.generateResponseHMAC(dataForVerification);

            // Compare signatures
            const isValid = receivedSignature.toUpperCase() === expectedSignature.toUpperCase();

            console.log('BSP signature verification:', {
                received: receivedSignature.substring(0, 10) + '...',
                expected: expectedSignature.substring(0, 10) + '...',
                isValid
            });

            return isValid;

        } catch (error) {
            console.error('BSP signature verification error:', error);
            return false;
        }
    }

    /**
     * Generate HMAC for BSP response verification
     * @param {Object} data - Response data from BSP
     * @returns {string} HMAC signature
     */
    generateResponseHMAC(data) {
        // BSP response checksum format (from documentation):
        // nar_cardNo|nar_cardType|nar_debitAuthCode|nar_debitAuthNo|nar_merId|nar_merTxnTime|nar_msgType|nar_narTxnId|nar_narTxnTime|nar_orderNo|nar_remarks|nar_remitterBankId|nar_remitterName|nar_txnAmount|nar_txnCurrency
        const fields = [
            data.nar_cardNo || '',
            data.nar_cardType || '',
            data.nar_debitAuthCode || '',
            data.nar_debitAuthNo || '',
            data.nar_merId || '',
            data.nar_merTxnTime || '',
            data.nar_msgType || '',
            data.nar_narTxnId || '',
            data.nar_narTxnTime || '',
            data.nar_orderNo || '',
            data.nar_remarks || '',
            data.nar_remitterBankId || '',
            data.nar_remitterName || '',
            data.nar_txnAmount || '',
            data.nar_txnCurrency || ''
        ];

        const stringToSign = fields.join('|');

        console.log('Response HMAC string to sign:', stringToSign);

        const hmac = crypto.createHmac('sha256', this.config.hmacKey);
        hmac.update(stringToSign, 'utf8');
        return hmac.digest('hex').toLowerCase(); // BSP uses lowercase for response
    }
    
    /**
     * Parse payment status from BSP response according to BSP IPG documentation
     * @param {Object} callbackData - BSP callback data with nar_ prefixed fields
     * @returns {Object} Parsed payment status
     */
    parsePaymentStatus(callbackData) {
        const status = {
            orderId: callbackData.nar_orderNo,
            transactionId: callbackData.nar_narTxnId || callbackData.nar_paymentId,
            amount: callbackData.nar_txnAmount,
            currency: callbackData.nar_txnCurrency === '242' ? 'FJD' : callbackData.nar_txnCurrency,
            status: this.normalizeBSPStatus(callbackData.nar_debitAuthCode),
            authCode: callbackData.nar_debitAuthCode,
            authNo: callbackData.nar_debitAuthNo,
            cardType: callbackData.nar_cardType,
            cardNo: callbackData.nar_cardNo,
            remitterName: callbackData.nar_remitterName,
            remarks: callbackData.nar_remarks,
            message: callbackData.nar_remarks || this.getStatusMessage(callbackData.nar_debitAuthCode),
            timestamp: callbackData.nar_narTxnTime || new Date().toISOString(),
            raw: callbackData
        };

        return status;
    }
    
    /**
     * Normalize BSP payment status codes according to BSP IPG documentation
     * @param {string} authCode - BSP debit authorization code
     * @returns {string} Normalized status
     */
    normalizeBSPStatus(authCode) {
        if (!authCode) return 'unknown';

        const code = authCode.toString().trim();

        // BSP Response Codes from documentation (Table 6)
        switch (code) {
            case '00':
                return 'success';  // Approved
            case '03':
            case '05':
            case '12':
            case '13':
            case '14':
            case '20':
            case '30':
            case '45':
            case '47':
            case '48':
            case '51':
            case '53':
            case '57':
            case '61':
            case '65':
            case '76':
            case '78':
            case '84':
            case '85':
            case '91':
            case 'N7':
                return 'failed';
            case '80':
            case 'UC':
                return 'cancelled';  // User cancelled
            case 'TO':
            case 'NF':
            case 'IM':
            case 'UN':
                return 'failed';
            default:
                return 'unknown';
        }
    }

    /**
     * Get human-readable message for BSP status codes
     * @param {string} authCode - BSP debit authorization code
     * @returns {string} Status message
     */
    getStatusMessage(authCode) {
        if (!authCode) return 'Unknown status';

        const code = authCode.toString().trim();

        // BSP Response Code descriptions from documentation
        const messages = {
            '00': 'Approved',
            '03': 'Invalid Merchant',
            '05': 'Do not honor',
            '12': 'Invalid Transaction',
            '13': 'Invalid Amount',
            '14': 'Invalid Customer Credentials',
            '20': 'Invalid Response',
            '30': 'Transaction Not Supported Or Format Error',
            '45': 'Duplicate Merchant Order Number',
            '47': 'Invalid Currency',
            '48': 'Transaction Limit Exceeded',
            '51': 'Insufficient Funds',
            '53': 'No Savings Account',
            '57': 'Transaction Not Permitted',
            '61': 'Withdrawal Limit Exceeded',
            '65': 'Withdrawal Frequency Exceeded',
            '76': 'Transaction Not Found',
            '78': 'Checksum Decryption Failed',
            '80': 'Buyer Cancel Transaction',
            '84': 'Invalid Transaction Type',
            '85': 'Internal Error At Bank System',
            'UC': 'Transaction Cancelled By Customer',
            'UN': 'Unknown error',
            'TO': 'Session Timeout at NARADA® Secure Entry Page',
            'NF': 'Txn Not Found',
            'IM': 'Invalid Message',
            '91': 'Issuer Declined or Transaction Timed out',
            'N7': 'Invalid CVV2'
        };

        return messages[code] || `Unknown status code: ${code}`;
    }
    
    /**
     * Escape HTML for security
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;
        
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return text.replace(/[&<>"']/g, (m) => map[m]);
    }
}

module.exports = BSPIntegration;

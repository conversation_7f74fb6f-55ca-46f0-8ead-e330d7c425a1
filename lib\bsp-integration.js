const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * BSP IPG Integration Library
 * Handles payment gateway integration with BSP Bank
 */
class BSPIntegration {
    constructor() {
        this.config = {
            hmacKey: process.env.BSP_HMAC_KEY,
            merchantId: process.env.MERCHANT_ID,
            merchantSid: process.env.MERCHANT_SID,
            merchantTid: process.env.MERCHANT_TID,
            mccCode: process.env.MCC_CODE,
            merchantName: process.env.MERCHANT_NAME,
            paymentUrl: process.env.BSP_PAYMENT_URL,
            returnUrl: process.env.RETURN_URL,
            websiteAddress: process.env.WEBSITE_ADDRESS
        };
        
        this.validateConfig();
    }
    
    /**
     * Validate required configuration
     */
    validateConfig() {
        const required = ['hmacKey', 'merchantId', 'merchantSid', 'merchantTid', 'paymentUrl', 'returnUrl'];
        const missing = required.filter(key => !this.config[key]);
        
        if (missing.length > 0) {
            throw new Error(`Missing required BSP configuration: ${missing.join(', ')}`);
        }
    }
    
    /**
     * Generate HMAC signature for BSP requests
     * @param {Object} data - Payment data
     * @returns {string} HMAC signature
     */
    generateHMAC(data) {
        try {
            // Create the string to sign based on BSP documentation
            const stringToSign = this.createStringToSign(data);
            
            // Generate HMAC-SHA256 signature
            const hmac = crypto.createHmac('sha256', this.config.hmacKey);
            hmac.update(stringToSign, 'utf8');
            const signature = hmac.digest('hex').toUpperCase();
            
            console.log('HMAC Generation:', {
                stringToSign,
                signature: signature.substring(0, 10) + '...' // Log partial signature for debugging
            });
            
            return signature;
        } catch (error) {
            console.error('HMAC generation error:', error);
            throw new Error('Failed to generate payment signature');
        }
    }
    
    /**
     * Create string to sign for HMAC generation
     * @param {Object} data - Payment data
     * @returns {string} String to sign
     */
    createStringToSign(data) {
        // Based on typical BSP IPG requirements - adjust based on actual documentation
        const fields = [
            data.merchantId || this.config.merchantId,
            data.merchantSid || this.config.merchantSid,
            data.merchantTid || this.config.merchantTid,
            data.orderId,
            data.amount,
            data.currency || 'FJD',
            data.returnUrl || this.config.returnUrl,
            data.timestamp
        ];
        
        return fields.join('|');
    }
    
    /**
     * Prepare payment request data for BSP gateway
     * @param {Object} donationData - Donation form data
     * @returns {Object} BSP payment request data
     */
    preparePaymentRequest(donationData) {
        try {
            const orderId = this.generateOrderId();
            const timestamp = new Date().toISOString();
            const amount = this.formatAmount(donationData.amount);
            
            const paymentData = {
                // Merchant Information
                merchantId: this.config.merchantId,
                merchantSid: this.config.merchantSid,
                merchantTid: this.config.merchantTid,
                mccCode: this.config.mccCode,
                merchantName: this.config.merchantName,
                
                // Transaction Information
                orderId: orderId,
                amount: amount,
                currency: 'FJD',
                description: `SPCA Fiji Donation - ${donationData.purpose || 'General Fund'}`,
                
                // URLs
                returnUrl: this.config.returnUrl,
                cancelUrl: `${this.config.websiteAddress}/cancel`,
                notifyUrl: `${this.config.websiteAddress}/api/payment-callback`,
                
                // Customer Information
                customerName: `${donationData.firstName} ${donationData.lastName}`,
                customerEmail: donationData.email,
                customerPhone: donationData.phone || '',
                customerCity: donationData.city || '',
                customerCountry: donationData.country || 'FJ',
                
                // Additional Information
                timestamp: timestamp,
                paymentMethod: donationData.paymentMethod || 'card',
                language: 'en',
                
                // Custom fields for tracking
                customField1: donationData.purpose || 'general',
                customField2: donationData.message ? donationData.message.substring(0, 100) : '',
                customField3: donationData.newsletter ? 'yes' : 'no'
            };
            
            // Generate HMAC signature
            paymentData.signature = this.generateHMAC(paymentData);
            
            return paymentData;
            
        } catch (error) {
            console.error('Payment request preparation error:', error);
            throw new Error('Failed to prepare payment request');
        }
    }
    
    /**
     * Generate unique order ID
     * @returns {string} Order ID
     */
    generateOrderId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8).toUpperCase();
        return `SPCA${timestamp}${random}`;
    }
    
    /**
     * Format amount for BSP gateway (typically in cents)
     * @param {number} amount - Amount in dollars
     * @returns {string} Formatted amount
     */
    formatAmount(amount) {
        const cents = Math.round(parseFloat(amount) * 100);
        return cents.toString();
    }
    
    /**
     * Create payment form HTML for auto-submission to BSP
     * @param {Object} paymentData - Payment request data
     * @returns {string} HTML form
     */
    createPaymentForm(paymentData) {
        const formFields = Object.entries(paymentData)
            .map(([key, value]) => `<input type="hidden" name="${key}" value="${this.escapeHtml(value)}">`)
            .join('\n');
        
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Redirecting to Payment Gateway...</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        text-align: center; 
                        padding: 50px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                    }
                    .loader { 
                        border: 4px solid #f3f3f3; 
                        border-top: 4px solid #667eea; 
                        border-radius: 50%; 
                        width: 40px; 
                        height: 40px; 
                        animation: spin 1s linear infinite; 
                        margin: 20px auto;
                    }
                    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                </style>
            </head>
            <body>
                <h2>Redirecting to Secure Payment Gateway...</h2>
                <div class="loader"></div>
                <p>Please wait while we redirect you to BSP Bank's secure payment page.</p>
                <p>If you are not redirected automatically, <a href="#" onclick="document.forms[0].submit();" style="color: #fff;">click here</a>.</p>
                
                <form method="POST" action="${this.config.paymentUrl}" id="paymentForm">
                    ${formFields}
                </form>
                
                <script>
                    // Auto-submit form after a short delay
                    setTimeout(function() {
                        document.getElementById('paymentForm').submit();
                    }, 2000);
                </script>
            </body>
            </html>
        `;
    }
    
    /**
     * Verify HMAC signature from BSP callback
     * @param {Object} callbackData - Data from BSP callback
     * @returns {boolean} Signature is valid
     */
    verifyCallback(callbackData) {
        try {
            const receivedSignature = callbackData.signature;
            if (!receivedSignature) {
                console.error('No signature in callback data');
                return false;
            }
            
            // Remove signature from data for verification
            const dataForVerification = { ...callbackData };
            delete dataForVerification.signature;
            
            // Generate expected signature
            const expectedSignature = this.generateHMAC(dataForVerification);
            
            // Compare signatures
            const isValid = receivedSignature.toUpperCase() === expectedSignature.toUpperCase();
            
            console.log('Signature verification:', {
                received: receivedSignature.substring(0, 10) + '...',
                expected: expectedSignature.substring(0, 10) + '...',
                isValid
            });
            
            return isValid;
            
        } catch (error) {
            console.error('Signature verification error:', error);
            return false;
        }
    }
    
    /**
     * Parse payment status from BSP response
     * @param {Object} callbackData - BSP callback data
     * @returns {Object} Parsed payment status
     */
    parsePaymentStatus(callbackData) {
        const status = {
            orderId: callbackData.orderId,
            transactionId: callbackData.transactionId || callbackData.txnId,
            amount: callbackData.amount,
            currency: callbackData.currency || 'FJD',
            status: this.normalizeStatus(callbackData.status || callbackData.responseCode),
            message: callbackData.message || callbackData.responseMessage || '',
            timestamp: callbackData.timestamp || new Date().toISOString(),
            raw: callbackData
        };
        
        return status;
    }
    
    /**
     * Normalize payment status codes
     * @param {string} status - Raw status from BSP
     * @returns {string} Normalized status
     */
    normalizeStatus(status) {
        if (!status) return 'unknown';
        
        const statusStr = status.toString().toLowerCase();
        
        // Common success indicators
        if (['00', '000', 'success', 'approved', 'completed'].includes(statusStr)) {
            return 'success';
        }
        
        // Common failure indicators
        if (['01', '001', 'failed', 'declined', 'rejected', 'error'].includes(statusStr)) {
            return 'failed';
        }
        
        // Pending/processing indicators
        if (['pending', 'processing', 'in_progress'].includes(statusStr)) {
            return 'pending';
        }
        
        return 'unknown';
    }
    
    /**
     * Escape HTML for security
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;
        
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return text.replace(/[&<>"']/g, (m) => map[m]);
    }
}

module.exports = BSPIntegration;
